#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
站点地址解析功能使用示例
展示如何使用重构后的解析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from merge_dbf import DBFMergerPro
import pandas as pd

def demo_parsing():
    """演示站点地址解析功能"""
    
    print("🚀 站点地址解析功能演示\n")
    
    # 创建解析器实例
    merger = DBFMergerPro()
    
    # 示例站点地址数据
    sample_data = [
        {
            "记录ID": 1,
            "站点名称": "北京站点1",
            "站点地址": "PC;IIP=************;IPORT=55101;LIP=*************;MAC=C53F15204522;HD=202502405423354354;PCN=HT15FRDHRTE0HTR5;CPU=BFEBFBFF000145Q2;PI=C^NTFS^100G;VOL=51A9-0052@GM=0;OPS=北京云-40;VER=0.0.10;OSV=ICE PACK 1 (BUI"
        },
        {
            "记录ID": 2,
            "站点名称": "上海站点1",
            "站点地址": "MA;IIP=*******;IPORT=0088;LIP=*************;MAC=ABCDEFGH;IMEI=NA;RMPN=15066668888;UMPN=NA;ICCID=NA;OSV=ANDROID12;IMSI=NA"
        },
        {
            "记录ID": 3,
            "站点名称": "移动设备1",
            "站点地址": "MobileMA;IIP=**********;IPORT=8080;IDFV=CE9E4EFC-9A3E-49CB-BE6B-0152036521E2;OSV=iOS 17.1;MAC=AA:BB:CC:DD:EE:FF"
        },
        {
            "记录ID": 4,
            "站点名称": "空地址测试",
            "站点地址": ""
        },
        {
            "记录ID": 5,
            "站点名称": "复杂地址",
            "站点地址": "BK;IIP=***********;IPORT=9999;MAC=112233445566;LOC=上海@浦东新区;VER=2.1.0;未知信息;另一个未知"
        }
    ]
    
    print("📊 原始数据:")
    for record in sample_data:
        print(f"ID: {record['记录ID']}, 站点: {record['站点名称']}")
        print(f"地址: {record['站点地址']}")
        print()
    
    print("-" * 80)
    print("🔍 解析结果:\n")
    
    # 收集所有可能的字段
    all_fields = set()
    parsed_records = []
    
    for record in sample_data:
        # 保留原始字段
        parsed_record = {k: v for k, v in record.items() if k != "站点地址"}
        
        # 解析站点地址
        if record["站点地址"]:
            parsed_fields = merger._recursive_split(record["站点地址"])
            parsed_record.update(parsed_fields)
            all_fields.update(parsed_fields.keys())
        
        parsed_records.append(parsed_record)
    
    # 显示解析结果
    for i, record in enumerate(parsed_records):
        print(f"记录 {record['记录ID']} ({record['站点名称']}) 解析结果:")
        
        # 显示设备类型
        if "设备类型" in record:
            print(f"  设备类型: {record['设备类型']}")
        
        # 显示网络信息
        network_fields = ["IIP", "IPORT", "LIP", "MAC"]
        network_info = {k: record.get(k, "") for k in network_fields if k in record}
        if network_info:
            print("  网络信息:")
            for k, v in network_info.items():
                print(f"    {k}: {v}")
        
        # 显示设备信息
        device_fields = ["HD", "PCN", "CPU", "IMEI", "IDFV", "OSV"]
        device_info = {k: record.get(k, "") for k in device_fields if k in record}
        if device_info:
            print("  设备信息:")
            for k, v in device_info.items():
                print(f"    {k}: {v}")
        
        # 显示其他信息
        other_fields = set(record.keys()) - {"记录ID", "站点名称", "设备类型"} - set(network_fields) - set(device_fields)
        if other_fields:
            print("  其他信息:")
            for field in sorted(other_fields):
                print(f"    {field}: {record[field]}")
        
        print()
    
    print("-" * 80)
    print("📈 统计信息:")
    print(f"总记录数: {len(parsed_records)}")
    print(f"解析出的字段总数: {len(all_fields)}")
    print(f"解析出的字段列表: {sorted(all_fields)}")
    
    # 创建DataFrame展示结果
    print("\n📋 表格形式展示 (前几个主要字段):")
    df = pd.DataFrame(parsed_records)
    
    # 选择主要字段显示
    main_fields = ["记录ID", "站点名称", "设备类型", "IIP", "IPORT", "MAC", "OSV"]
    display_fields = [f for f in main_fields if f in df.columns]
    
    print(df[display_fields].to_string(index=False))
    
    print(f"\n✅ 演示完成！共解析出 {len(all_fields)} 个不同的字段。")

if __name__ == "__main__":
    demo_parsing()
