#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终综合测试 - 验证所有关键问题已解决
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from merge_dbf import DBFMergerPro
import pandas as pd

def comprehensive_test():
    """综合测试所有关键功能"""
    
    print("🎯 最终综合测试开始\n")
    print("="*80)
    
    merger = DBFMergerPro()
    
    # 综合测试用例 - 涵盖所有关键场景
    test_cases = [
        {
            "name": "字段顺序测试1",
            "input": "MA;IIP=*******;MAC=ABCDEFG;UMPN=15500001123;IMEI=123456789012345;RMPN=13800138000",
            "key_fields": ["设备类型", "IIP", "MAC", "UMPN", "IMEI", "RMPN"],
            "expected_device": "MA"
        },
        {
            "name": "字段顺序测试2 (重新排列)",
            "input": "MA;RMPN=13800138000;IMEI=123456789012345;IIP=*******;UMPN=15500001123;MAC=ABCDEFG",
            "key_fields": ["设备类型", "IIP", "MAC", "UMPN", "IMEI", "RMPN"],
            "expected_device": "MA"
        },
        {
            "name": "PC设备顺序测试1",
            "input": "PC;IIP=*******;CPU=541561546456465;MAC=BCEDFGH;HD=987654321098765;VER=2.1.0",
            "key_fields": ["设备类型", "IIP", "CPU", "MAC", "HD", "VER"],
            "expected_device": "PC"
        },
        {
            "name": "PC设备顺序测试2 (重新排列)",
            "input": "PC;HD=987654321098765;VER=2.1.0;MAC=BCEDFGH;CPU=541561546456465;IIP=*******",
            "key_fields": ["设备类型", "IIP", "CPU", "MAC", "HD", "VER"],
            "expected_device": "PC"
        },
        {
            "name": "超长数字测试",
            "input": "BK;IIP=********;HD=12345678901234567890123456789012345678901234567890;IMEI=123456789012345678901234567890;MAC=001122334455",
            "key_fields": ["设备类型", "IIP", "HD", "IMEI", "MAC"],
            "expected_device": "BK",
            "long_numbers": ["HD", "IMEI"]
        },
        {
            "name": "特殊符号测试",
            "input": "MI;IIP=*************;IDFV=ABC-DEF@GHI^JKL(MNO);PI=C^NTFS^100G;VOL=51A9-0052@GM=0;OPS=北京-朝阳区",
            "key_fields": ["设备类型", "IIP", "IDFV", "PI", "VOL", "OPS"],
            "expected_device": "MI"
        }
    ]
    
    print("🧪 执行综合测试用例...\n")
    
    all_results = []
    test_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试用例 {i}: {test_case['name']}")
        print(f"输入: {test_case['input']}")
        
        # 解析
        result = merger._recursive_split(test_case['input'])
        all_results.append({**result, "测试用例": test_case['name']})
        
        # 验证设备类型
        if result.get("设备类型") == test_case["expected_device"]:
            print(f"✅ 设备类型正确: {result.get('设备类型')}")
        else:
            print(f"❌ 设备类型错误: 期望 {test_case['expected_device']}, 实际 {result.get('设备类型')}")
            test_passed = False
        
        # 验证关键字段存在
        missing_fields = []
        for field in test_case["key_fields"]:
            if field not in result:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ 缺失字段: {missing_fields}")
            test_passed = False
        else:
            print(f"✅ 所有关键字段存在: {test_case['key_fields']}")
        
        # 验证长数字格式
        if "long_numbers" in test_case:
            for field in test_case["long_numbers"]:
                if field in result:
                    value = result[field]
                    if isinstance(value, str) and len(value) > 15:
                        print(f"✅ 长数字字段 {field} 格式正确: {value[:20]}...")
                    else:
                        print(f"❌ 长数字字段 {field} 格式可能有问题: {value}")
                        test_passed = False
        
        print("-" * 60)
    
    # 验证字段顺序独立性
    print("\n🔍 验证字段顺序独立性...")
    
    # 比较MA设备的两个不同顺序结果
    ma_results = [r for r in all_results if r.get("设备类型") == "MA"]
    if len(ma_results) >= 2:
        ma1, ma2 = ma_results[0], ma_results[1]
        ma_fields = ["IIP", "MAC", "UMPN", "IMEI", "RMPN"]
        
        order_independent = True
        for field in ma_fields:
            if ma1.get(field) != ma2.get(field):
                print(f"❌ MA设备字段 {field} 顺序依赖: {ma1.get(field)} vs {ma2.get(field)}")
                order_independent = False
                test_passed = False
        
        if order_independent:
            print("✅ MA设备字段顺序独立性验证通过")
    
    # 比较PC设备的两个不同顺序结果
    pc_results = [r for r in all_results if r.get("设备类型") == "PC"]
    if len(pc_results) >= 2:
        pc1, pc2 = pc_results[0], pc_results[1]
        pc_fields = ["IIP", "CPU", "MAC", "HD", "VER"]
        
        order_independent = True
        for field in pc_fields:
            if pc1.get(field) != pc2.get(field):
                print(f"❌ PC设备字段 {field} 顺序依赖: {pc1.get(field)} vs {pc2.get(field)}")
                order_independent = False
                test_passed = False
        
        if order_independent:
            print("✅ PC设备字段顺序独立性验证通过")
    
    # 测试CSV输出
    print("\n💾 测试CSV输出格式...")
    
    # 设置active_columns
    all_fields = set()
    for result in all_results:
        all_fields.update(result.keys())
    merger.active_columns = all_fields - {"测试用例"}
    
    df = pd.DataFrame(all_results)
    csv_file = "final_test_output.csv"
    
    try:
        merger.output_path = csv_file
        merger._write_csv(df)
        
        # 检查CSV内容
        with open(csv_file, 'r', encoding='utf_8_sig') as f:
            content = f.read()
        
        # 检查科学计数法
        scientific_found = any(pattern in content for pattern in ['e+', 'e-', 'E+', 'E-'])
        
        if scientific_found:
            print("❌ CSV输出包含科学计数法")
            test_passed = False
        else:
            print("✅ CSV输出格式正确，无科学计数法")
        
        # 检查长数字完整性
        long_test_numbers = [
            "12345678901234567890123456789012345678901234567890",
            "123456789012345678901234567890",
            "541561546456465",
            "987654321098765"
        ]
        
        preserved_count = sum(1 for num in long_test_numbers if num in content)
        print(f"✅ 长数字完整保存: {preserved_count}/{len(long_test_numbers)}")
        
    except Exception as e:
        print(f"❌ CSV测试失败: {e}")
        test_passed = False
    finally:
        if os.path.exists(csv_file):
            os.remove(csv_file)
    
    return test_passed

def main():
    """主函数"""
    print("🚀 DBF站点地址解析 - 最终验证测试")
    print("验证目标:")
    print("1. ✅ 字段解析基于字段名，不依赖位置顺序")
    print("2. ✅ 数值格式完全保护，避免科学计数法")
    print("3. ✅ 特殊符号正确处理")
    print("4. ✅ 中文内容支持")
    print("5. ✅ CSV输出格式正确")
    print()
    
    # 执行综合测试
    success = comprehensive_test()
    
    print("\n" + "="*80)
    print("🏁 最终测试结果:")
    
    if success:
        print("🎉 所有测试通过！")
        print()
        print("✅ 关键问题已解决:")
        print("   • 字段顺序依赖性问题 - 已修复")
        print("   • CSV科学计数法问题 - 已修复")
        print()
        print("🚀 DBF站点地址解析功能已完全优化，可以安全使用！")
    else:
        print("❌ 存在未解决的问题，需要进一步检查！")
    
    return success

if __name__ == "__main__":
    main()
