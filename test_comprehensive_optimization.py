#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合优化测试 - 验证所有新增功能
1. 11位纯数字预检查
2. 金额字段小数精度
3. 科学计数法完全防护
4. 列顺序控制
5. 字段映射准确性
6. 原始字段保留
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from merge_dbf import DBFMergerPro
import pandas as pd

def test_11_digit_precheck():
    """测试11位纯数字预检查功能"""
    print("🧪 测试11位纯数字预检查功能...\n")
    
    merger = DBFMergerPro()
    
    test_cases = [
        {
            "name": "11位纯数字",
            "input": "13800138000",
            "expected_other_info": "13800138000",
            "should_skip_parsing": True
        },
        {
            "name": "11位数字但包含其他字符",
            "input": "MA;13800138000",
            "expected_other_info": "",
            "should_skip_parsing": False
        },
        {
            "name": "10位数字",
            "input": "1380013800",
            "expected_other_info": "1380013800",
            "should_skip_parsing": True
        },
        {
            "name": "12位数字",
            "input": "138001380001",
            "expected_other_info": "138001380001", 
            "should_skip_parsing": True
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试用例 {i}: {test_case['name']}")
        print(f"输入: '{test_case['input']}'")
        
        result = merger._enhanced_recursive_split(test_case['input'])
        
        print(f"解析结果: {result}")
        
        # 验证11位纯数字处理
        if test_case['should_skip_parsing'] and len(test_case['input']) == 11 and test_case['input'].isdigit():
            if result.get("其他站点信息") == test_case['expected_other_info'] and len(result) == 1:
                print("✅ 11位纯数字正确跳过解析")
            else:
                print("❌ 11位纯数字处理错误")
                all_passed = False
        else:
            print("✅ 非11位纯数字正常处理")
        
        print("-" * 60)
    
    return all_passed

def test_field_mapping_accuracy():
    """测试字段映射准确性 - 最关键的测试"""
    print("\n🎯 测试字段映射准确性（最关键）...\n")
    
    merger = DBFMergerPro()
    
    # 设计容易出错的测试用例
    test_cases = [
        {
            "name": "ICCID和RMPN混合测试1",
            "input": "MA;IIP=*******;ICCID=89860123456789012345;RMPN=13800138000;IMEI=123456789012345",
            "expected_mapping": {
                "设备类型": "MA",
                "IIP": "*******", 
                "ICCID": "89860123456789012345",
                "RMPN": "13800138000",
                "IMEI": "123456789012345"
            }
        },
        {
            "name": "ICCID和RMPN混合测试2（顺序颠倒）",
            "input": "MA;RMPN=13800138000;IIP=*******;IMEI=123456789012345;ICCID=89860123456789012345",
            "expected_mapping": {
                "设备类型": "MA",
                "IIP": "*******",
                "ICCID": "89860123456789012345", 
                "RMPN": "13800138000",
                "IMEI": "123456789012345"
            }
        },
        {
            "name": "复杂字段混合",
            "input": "PC;HD=987654321098765;CPU=541561546456465;IIP=*******;MAC=BCEDFGH;VER=2.1.0",
            "expected_mapping": {
                "设备类型": "PC",
                "HD": "987654321098765",
                "CPU": "541561546456465",
                "IIP": "*******",
                "MAC": "BCEDFGH",
                "VER": "2.1.0"
            }
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试用例 {i}: {test_case['name']}")
        print(f"输入: {test_case['input']}")
        
        result = merger._enhanced_recursive_split(test_case['input'])
        
        print(f"解析结果: {result}")
        print(f"期望映射: {test_case['expected_mapping']}")
        
        # 验证每个字段的映射
        mapping_correct = True
        for field, expected_value in test_case['expected_mapping'].items():
            if field not in result:
                print(f"❌ 缺失字段: {field}")
                mapping_correct = False
                all_passed = False
            elif result[field] != expected_value:
                print(f"❌ 字段 '{field}' 映射错误:")
                print(f"   期望: '{expected_value}'")
                print(f"   实际: '{result[field]}'")
                mapping_correct = False
                all_passed = False
        
        if mapping_correct:
            print("✅ 所有字段映射正确")
        
        print("-" * 60)
    
    return all_passed

def test_scientific_notation_prevention():
    """测试科学计数法防护"""
    print("\n🔬 测试科学计数法防护...\n")
    
    merger = DBFMergerPro()
    
    # 设置active_columns以包含测试字段
    merger.active_columns = {"设备类型", "IIP", "LIP", "HD", "CPU", "IMEI", "RMPN", "ICCID", "MAC"}
    
    test_data = [
        {
            "站点地址": "PC;IIP=192168001100;LIP=192168001101;HD=123456789012345678901234567890;CPU=987654321098765432109876543210",
            "测试ID": 1
        },
        {
            "站点地址": "MA;IMEI=123456789012345678;RMPN=13800138000123;ICCID=89860123456789012345678901234567890",
            "测试ID": 2
        }
    ]
    
    # 处理数据
    processed_data = []
    for record in test_data:
        processed = merger._process_record(record)
        processed_data.append(processed)
    
    # 创建DataFrame并测试CSV输出
    df = pd.DataFrame(processed_data)
    
    print("DataFrame中的长数字字段:")
    long_number_fields = ["IIP", "LIP", "HD", "CPU", "IMEI", "RMPN", "ICCID"]
    for field in long_number_fields:
        if field in df.columns:
            for idx, val in df[field].items():
                if pd.notna(val) and str(val) != '':
                    print(f"  {field}[{idx}]: '{val}' (类型: {type(val).__name__})")
    
    # 测试CSV输出
    csv_file = "test_scientific_notation.csv"
    try:
        merger.output_path = csv_file
        merger._write_csv(df)
        
        # 检查CSV内容
        with open(csv_file, 'r', encoding='utf_8_sig') as f:
            content = f.read()
        
        print("\nCSV文件内容检查:")
        lines = content.split('\n')[:5]
        for i, line in enumerate(lines):
            if line.strip():
                print(f"  行{i+1}: {line}")
        
        # 检查科学计数法
        scientific_patterns = ['e+', 'e-', 'E+', 'E-']
        found_scientific = any(pattern in content for pattern in scientific_patterns)
        
        if found_scientific:
            print("❌ 发现科学计数法格式")
            return False
        else:
            print("✅ 未发现科学计数法格式")
            return True
            
    except Exception as e:
        print(f"❌ CSV测试失败: {e}")
        return False
    finally:
        if os.path.exists(csv_file):
            os.remove(csv_file)

def test_column_ordering():
    """测试列顺序控制"""
    print("\n📋 测试列顺序控制...\n")
    
    merger = DBFMergerPro()
    merger.active_columns = {"设备类型", "IIP", "MAC", "RMPN"}
    
    test_data = [
        {
            "原始字段1": "值1",
            "站点地址": "MA;IIP=*******;MAC=ABCDEF;RMPN=13800138000",
            "原始字段2": "值2",
            "其他站点信息": "测试信息"
        }
    ]
    
    processed_data = []
    for record in test_data:
        processed = merger._process_record(record)
        processed_data.append(processed)
    
    df = pd.DataFrame(processed_data)
    df = merger._reorder_columns(df)
    
    print("列顺序:")
    for i, col in enumerate(df.columns):
        print(f"  {i+1}. {col}")
    
    # 验证"其他站点信息"是否在最后
    if df.columns[-1] == "其他站点信息":
        print("✅ '其他站点信息'字段正确位于最后")
        return True
    else:
        print("❌ '其他站点信息'字段位置错误")
        return False

def main():
    """主测试函数"""
    print("🚀 综合优化功能测试开始\n")
    print("="*80)
    
    # 执行所有测试
    test_results = {
        "11位纯数字预检查": test_11_digit_precheck(),
        "字段映射准确性": test_field_mapping_accuracy(), 
        "科学计数法防护": test_scientific_notation_prevention(),
        "列顺序控制": test_column_ordering()
    }
    
    print("\n" + "="*80)
    print("📊 综合测试结果:")
    
    all_passed = True
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "="*80)
    if all_passed:
        print("🎉 所有综合优化功能测试通过！")
        print("✅ 站点地址解析功能已完全优化")
    else:
        print("⚠️ 部分功能需要进一步优化")
    
    return all_passed

if __name__ == "__main__":
    main()
