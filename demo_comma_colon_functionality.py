#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
逗号冒号格式功能演示
展示新增的逗号冒号(,)分隔符解析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from merge_dbf import DBFMergerPro
import pandas as pd

def demonstrate_comma_colon_features():
    """演示逗号冒号格式的所有新功能"""
    
    print("🚀 逗号冒号分隔符格式功能演示")
    print("="*80)
    
    merger = DBFMergerPro()
    
    # 模拟真实的三种格式混合数据
    sample_records = [
        {
            "记录ID": 1,
            "站点名称": "传统分号格式站点",
            "站点地址": "MA;IIP=*******;MAC=ABCDEF;RMPN=13800138000;IMEI=123456789012345",
            "数据来源": "传统DBF文件",
            "格式类型": "分号格式"
        },
        {
            "记录ID": 2,
            "站点名称": "管道格式站点",
            "站点地址": "MA|IIP=*************|IPORT=443|LIP=NA|MAC=NA|IMEI=NA|RMPN=NA|UMPN=NA|ICCID=NA|OSV=NA|IMSI=NA|NA|mobile",
            "数据来源": "新输出文件",
            "格式类型": "管道格式"
        },
        {
            "记录ID": 3,
            "站点名称": "逗号冒号格式站点A",
            "站点地址": "MAC:02:00:00:00:00:00,UDID:BAAFE101-2JM5-0B5T-04CV-VFDGVFD1S,LIP:************,OSV:16.4.2",
            "数据来源": "新发现格式",
            "格式类型": "逗号冒号格式"
        },
        {
            "记录ID": 4,
            "站点名称": "逗号冒号格式站点B（含IPv6）",
            "站点地址": "LIP6:fe80::a11:c3y7:6ef7:01vc,IDFV:5VDS9283P-9C14-75F9-0000-VRDESG67R,MAC:AA:BB:CC:DD:EE:FF,OSV:iOS 17.1",
            "数据来源": "新发现格式",
            "格式类型": "逗号冒号格式"
        },
        {
            "记录ID": 5,
            "站点名称": "复杂逗号冒号格式（含前缀）",
            "站点地址": "HDInfo=MAC:02:00:00:00:00:00,UDID:BAAFE101-2JM5-0B5T-04CV-VFDGVFD1S,LIP:************,OSV:16.4.2,LIP6:fe80::a11:c3y7:6ef7:01vc,IDFV:5VDS9283P-9C14-75F9-0000-VRDESG67R",
            "数据来源": "用户提供的实际示例",
            "格式类型": "逗号冒号格式（含前缀）"
        }
    ]
    
    print("📊 原始三种格式混合数据:")
    for record in sample_records:
        print(f"\n记录 {record['记录ID']}: {record['站点名称']} ({record['格式类型']})")
        print(f"  站点地址: {record['站点地址']}")
        print(f"  数据来源: {record['数据来源']}")
    
    print("\n" + "-" * 80)
    
    # 演示格式检测
    print("\n🔍 三种格式检测演示:")
    for record in sample_records:
        address = record['站点地址']
        format_type, parts = merger._detect_format_and_split(address)
        print(f"\n记录 {record['记录ID']}:")
        print(f"  检测到的格式: '{format_type}'")
        print(f"  分割后的部分数: {len(parts)}")
        print(f"  前3个部分: {parts[:3]}")
        
        # 特别标注逗号冒号格式
        if format_type == 'comma_colon':
            print(f"  ⭐ 新格式：逗号冒号分隔符")
    
    print("\n" + "-" * 80)
    
    # 演示解析结果
    print("\n⚙️ 解析结果演示:")
    
    # 设置active_columns以包含所有可能的字段
    all_fields = set()
    for record in sample_records:
        parsed = merger._enhanced_recursive_split(record['站点地址'])
        all_fields.update(parsed.keys())
    
    merger.active_columns = all_fields - {"其他站点信息"}
    
    processed_records = []
    for record in sample_records:
        print(f"\n记录 {record['记录ID']} 解析:")
        
        parsed_fields = merger._enhanced_recursive_split(record['站点地址'])
        print(f"  解析出的字段: {list(parsed_fields.keys())}")
        
        # 特别展示关键字段
        key_fields = ["设备类型", "MAC", "LIP6", "UDID", "IDFV", "IIP"]
        for field in key_fields:
            if field in parsed_fields:
                value = parsed_fields[field]
                if field in ["MAC", "LIP6"] and ":" in str(value):
                    print(f"  {field}: '{value}' ⭐ (复杂值保护)")
                elif field == "设备类型":
                    print(f"  {field}: '{value}' (传统格式)")
                else:
                    print(f"  {field}: '{value}'")
        
        # 处理完整记录
        processed = merger._process_record(record)
        processed_records.append(processed)
    
    print("\n" + "-" * 80)
    
    # 创建DataFrame展示结果
    print("\n📋 处理后的数据结构:")
    df = pd.DataFrame(processed_records)
    df = merger._reorder_columns(df)
    
    print(f"总列数: {len(df.columns)}")
    print(f"原始字段: {[col for col in df.columns if col in ['记录ID', '站点名称', '站点地址', '数据来源', '格式类型']]}")
    print(f"解析字段: {[col for col in df.columns if col in merger.active_columns]}")
    print(f"特殊字段: {[col for col in df.columns if col == '其他站点信息']}")
    
    # 展示关键功能验证
    print("\n" + "-" * 80)
    print("\n🎯 关键功能验证:")
    
    # 1. 三种格式兼容性
    print("\n1️⃣ 三种格式兼容性验证:")
    format_counts = {}
    for record in processed_records:
        format_type = record.get('格式类型', 'Unknown')
        format_counts[format_type] = format_counts.get(format_type, 0) + 1
    
    for format_type, count in format_counts.items():
        print(f"   {format_type}: {count} 个记录")
    print(f"   ✅ 三种格式混合数据集处理成功")
    
    # 2. 复杂值保护验证
    print("\n2️⃣ 复杂值保护验证:")
    complex_values = [
        ("MAC地址", "02:00:00:00:00:00"),
        ("IPv6地址", "fe80::a11:c3y7:6ef7:01vc"),
        ("复杂UDID", "BAAFE101-2JM5-0B5T-04CV-VFDGVFD1S")
    ]
    
    for value_type, expected_value in complex_values:
        found = False
        for record in processed_records:
            for field, value in record.items():
                if str(value) == expected_value:
                    print(f"   ✅ {value_type}: '{value}' 正确保护")
                    found = True
                    break
            if found:
                break
    
    # 3. 设备类型字段验证
    print("\n3️⃣ 设备类型字段验证:")
    for record in processed_records:
        format_type = record.get('格式类型', '')
        device_type = record.get('设备类型', '')
        
        if '逗号冒号' in format_type:
            if not device_type or device_type == '':
                print(f"   记录 {record['记录ID']}: ✅ 逗号冒号格式正确无设备类型")
            else:
                print(f"   记录 {record['记录ID']}: ⚠️ 逗号冒号格式不应有设备类型: {device_type}")
        else:
            if device_type:
                print(f"   记录 {record['记录ID']}: ✅ {format_type}设备类型: {device_type}")
    
    # 4. HDInfo前缀处理验证
    print("\n4️⃣ HDInfo前缀处理验证:")
    for record in processed_records:
        if 'HDInfo' in record.get('站点地址', ''):
            other_info = record.get('其他站点信息', '')
            if 'HDInfo' in other_info:
                print(f"   记录 {record['记录ID']}: ✅ HDInfo前缀正确放入其他站点信息")
            else:
                print(f"   记录 {record['记录ID']}: ❌ HDInfo前缀处理错误")
    
    # 5. CSV输出测试
    print("\n5️⃣ CSV输出格式验证:")
    csv_file = "demo_comma_colon_output.csv"
    try:
        merger.output_path = csv_file
        merger._write_csv(df)
        
        # 检查CSV内容
        with open(csv_file, 'r', encoding='utf_8_sig') as f:
            content = f.read()
        
        # 检查复杂值是否正确保存
        complex_test_values = [
            "02:00:00:00:00:00",  # MAC地址
            "fe80::a11:c3y7:6ef7:01vc",  # IPv6地址
            "BAAFE101-2JM5-0B5T-04CV-VFDGVFD1S"  # 复杂UDID
        ]
        
        preserved_count = sum(1 for val in complex_test_values if val in content)
        print(f"   ✅ 复杂值格式保持: {preserved_count}/{len(complex_test_values)}")
        
        # 检查三种格式原始地址是否保留
        original_addresses = [r['站点地址'] for r in sample_records]
        preserved_addresses = sum(1 for addr in original_addresses if addr in content)
        print(f"   ✅ 原始地址保留: {preserved_addresses}/{len(original_addresses)}")
        
        # 显示CSV文件信息
        lines = content.split('\n')
        print(f"   ✅ CSV文件生成成功: {len(lines)-1} 行数据")
        
    except Exception as e:
        print(f"   ❌ CSV测试失败: {e}")
    finally:
        if os.path.exists(csv_file):
            os.remove(csv_file)
    
    print("\n" + "="*80)
    print("🎉 逗号冒号分隔符格式功能演示完成！")
    print()
    print("✅ 新增功能总结:")
    print("   • 自动检测逗号冒号(,)分隔符格式")
    print("   • 智能冒号分割（仅第一个冒号），保护复杂值")
    print("   • MAC地址和IPv6地址完整保护")
    print("   • HDInfo=前缀智能处理")
    print("   • 无设备类型字段（符合格式特征）")
    print("   • 三种格式混合数据集无缝兼容")
    print("   • 完全向后兼容分号和管道格式")
    print("   • 保持所有现有优化功能")
    print()
    print("🚀 扩展后的DBF合并工具现在支持三种数据格式，功能更加全面！")

def demonstrate_real_world_examples():
    """演示真实世界的例子"""
    print("\n" + "="*80)
    print("🌍 真实世界应用示例")
    print("="*80)
    
    merger = DBFMergerPro()
    
    # 基于用户提供的真实格式
    real_examples = [
        {
            "描述": "用户发现的逗号冒号格式",
            "地址": "HDInfo=MAC:02:00:00:00:00:00,UDID:BAAFE101-2JM5-0B5T-04CV-VFDGVFD1S,LIP:************,OSV:16.4.2,LIP6:fe80::a11:c3y7:6ef7:01vc,IDFV:5VDS9283P-9C14-75F9-0000-VRDESG67R",
            "预期问题": "新发现的格式，需要正确解析所有字段"
        },
        {
            "描述": "包含IPv6的复杂格式",
            "地址": "LIP6:fe80::a11:c3y7:6ef7:01vc,MAC:AA:BB:CC:DD:EE:FF,TIMESTAMP:2024-01-15T10:30:45.123Z",
            "预期问题": "IPv6地址中的双冒号需要保护"
        },
        {
            "描述": "简单逗号冒号格式",
            "地址": "MAC:02:00:00:00:00:00,TYPE:ethernet,STATUS:active",
            "预期问题": "基本的逗号冒号格式解析"
        }
    ]
    
    # 设置必要的字段
    merger.active_columns = {
        "MAC", "UDID", "LIP", "OSV", "LIP6", "IDFV", "TYPE", "STATUS", "TIMESTAMP"
    }
    
    for i, example in enumerate(real_examples, 1):
        print(f"\n示例 {i}: {example['描述']}")
        print(f"原始地址: {example['地址']}")
        print(f"处理目标: {example['预期问题']}")
        
        # 展示格式检测
        format_type, parts = merger._detect_format_and_split(example['地址'])
        print(f"格式检测: {format_type} (分割为 {len(parts)} 个部分)")
        
        # 展示解析结果
        result = merger._enhanced_recursive_split(example['地址'])
        
        print("解析结果:")
        for field, value in sorted(result.items()):
            if field in ["MAC", "LIP6"] and ":" in str(value):
                print(f"  {field}: '{value}' ⭐ (复杂值保护)")
            elif field == "其他站点信息":
                print(f"  {field}: '{value}' (前缀信息)")
            else:
                print(f"  {field}: '{value}'")
        
        print(f"✅ 成功解析出 {len(result)} 个字段")
        print("-" * 60)

if __name__ == "__main__":
    demonstrate_comma_colon_features()
    demonstrate_real_world_examples()
