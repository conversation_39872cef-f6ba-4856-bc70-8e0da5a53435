#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化功能演示 - 展示所有新增和改进的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from merge_dbf import DBFMergerPro
import pandas as pd

def demonstrate_optimized_features():
    """演示所有优化功能"""
    
    print("🚀 DBF站点地址解析 - 优化功能演示")
    print("="*80)
    
    merger = DBFMergerPro()
    
    # 模拟真实的DBF记录数据
    sample_records = [
        {
            "记录ID": 1,
            "站点名称": "北京站点A",
            "站点地址": "PC;IIP=120161000015;IPORT=55101;LIP=192168001101;MAC=C53F15204522;HD=202502405423354354;PCN=HT15FRDHRTE0HTR5;CPU=BFEBFBFF000145Q2;PI=C^NTFS^100G;VOL=51A9-0052@GM=0;OPS=北京云-40;VER=0.0.10;OSV=ICE PACK 1 (BUI",
            "发生金额": 1234.567,
            "后资金额": 5678.9,
            "创建时间": "2024-01-15"
        },
        {
            "记录ID": 2,
            "站点名称": "上海站点B", 
            "站点地址": "MA;RMPN=13800138000;IIP=192168001100;IMEI=123456789012345678;ICCID=89860123456789012345678901234567890;UMPN=13912345678;OSV=ANDROID12",
            "发生金额": 999.1,
            "后资金额": 1111.22,
            "创建时间": "2024-01-16"
        },
        {
            "记录ID": 3,
            "站点名称": "移动设备C",
            "站点地址": "MobileMA;IDFV=CE9E4EFC-9A3E-49CB-BE6B-0152036521E2;IIP=10000000100;MAC=AA1BB2CC3DD4;OSV=iOS 17.1",
            "发生金额": 500,
            "后资金额": 750.0,
            "创建时间": "2024-01-17"
        },
        {
            "记录ID": 4,
            "站点名称": "纯数字地址测试",
            "站点地址": "13800138000",  # 11位纯数字，应该跳过解析
            "发生金额": 123.45,
            "后资金额": 678.90,
            "创建时间": "2024-01-18"
        },
        {
            "记录ID": 5,
            "站点名称": "字段顺序测试",
            "站点地址": "BK;HD=987654321098765432109876543210;MAC=112233445566;IIP=172016001050;IPORT=9999;LOC=上海@浦东新区;VER=2.1.0;未知信息;另一个未知",
            "发生金额": 2000.999,
            "后资金额": 3000.001,
            "创建时间": "2024-01-19"
        }
    ]
    
    print("📊 原始数据展示:")
    for record in sample_records:
        print(f"  记录{record['记录ID']}: {record['站点名称']}")
        print(f"    站点地址: {record['站点地址']}")
        print(f"    发生金额: {record['发生金额']}, 后资金额: {record['后资金额']}")
        print()
    
    print("-" * 80)
    
    # 第一阶段：全面扫描字段模式（模拟preprocess阶段）
    print("🔍 第一阶段：全面扫描字段模式...")
    
    all_field_patterns = set()
    for record in sample_records:
        if record.get("站点地址"):
            parsed_fields = merger._enhanced_recursive_split(record["站点地址"])
            for field_name in parsed_fields.keys():
                if field_name != "其他站点信息":
                    all_field_patterns.add(field_name)
    
    merger.active_columns = all_field_patterns
    print(f"识别到的字段模式: {sorted(all_field_patterns)}")
    print()
    
    # 第二阶段：处理记录
    print("⚙️ 第二阶段：处理记录...")
    
    processed_records = []
    for record in sample_records:
        processed = merger._process_record(record)
        processed_records.append(processed)
    
    # 创建DataFrame并重新排列列
    df = pd.DataFrame(processed_records)
    df = merger._reorder_columns(df)
    
    print("📋 处理后的数据结构:")
    print(f"总列数: {len(df.columns)}")
    print("列顺序:")
    for i, col in enumerate(df.columns, 1):
        print(f"  {i:2d}. {col}")
    print()
    
    # 展示关键功能验证
    print("🎯 关键功能验证:")
    print()
    
    # 1. 11位纯数字预检查
    print("1️⃣ 11位纯数字预检查:")
    record_4 = processed_records[3]  # 记录4是11位纯数字
    if record_4.get("其他站点信息") == "13800138000" and not record_4.get("设备类型"):
        print("   ✅ 11位纯数字正确跳过解析，放入'其他站点信息'")
    else:
        print("   ❌ 11位纯数字处理错误")
    print()
    
    # 2. 金额字段小数精度
    print("2️⃣ 金额字段小数精度保持:")
    for i, record in enumerate(processed_records, 1):
        if "发生金额" in record and "后资金额" in record:
            print(f"   记录{i}: 发生金额={record['发生金额']}, 后资金额={record['后资金额']}")
    print()
    
    # 3. 字段映射准确性
    print("3️⃣ 字段映射准确性验证:")
    record_2 = processed_records[1]  # 记录2包含ICCID和RMPN
    if (record_2.get("ICCID") == "89860123456789012345678901234567890" and 
        record_2.get("RMPN") == "13800138000" and
        record_2.get("IMEI") == "123456789012345678"):
        print("   ✅ ICCID、RMPN、IMEI字段映射完全正确，无错位")
    else:
        print("   ❌ 字段映射存在错误")
    print()
    
    # 4. 原始字段保留
    print("4️⃣ 原始字段保留:")
    if all("站点地址" in record for record in processed_records):
        print("   ✅ 所有记录的原始'站点地址'字段都已保留")
    else:
        print("   ❌ 原始'站点地址'字段丢失")
    print()
    
    # 5. 列顺序控制
    print("5️⃣ 列顺序控制:")
    if df.columns[-1] == "其他站点信息":
        print("   ✅ '其他站点信息'字段正确位于最后一列")
    else:
        print("   ❌ '其他站点信息'字段位置错误")
    print()
    
    # 6. CSV输出测试
    print("6️⃣ CSV输出格式测试:")
    csv_file = "demo_optimized_output.csv"
    try:
        merger.output_path = csv_file
        merger._write_csv(df)
        
        # 检查CSV内容
        with open(csv_file, 'r', encoding='utf_8_sig') as f:
            content = f.read()
        
        # 检查长数字是否保持格式
        long_numbers = [
            "120161000015",  # IIP
            "192168001101",  # LIP  
            "202502405423354354",  # HD
            "89860123456789012345678901234567890",  # ICCID
            "987654321098765432109876543210"  # HD
        ]
        
        preserved_count = sum(1 for num in long_numbers if num in content)
        print(f"   长数字格式保持: {preserved_count}/{len(long_numbers)}")
        
        # 检查科学计数法
        has_scientific = any(pattern in content for pattern in ['e+', 'e-', 'E+', 'E-'])
        print(f"   科学计数法防护: {'❌ 发现科学计数法' if has_scientific else '✅ 无科学计数法'}")
        
        # 显示CSV前几行
        print("   CSV文件前3行预览:")
        lines = content.split('\n')[:3]
        for i, line in enumerate(lines, 1):
            if line.strip():
                # 截断过长的行以便显示
                display_line = line[:100] + "..." if len(line) > 100 else line
                print(f"     行{i}: {display_line}")
        
    except Exception as e:
        print(f"   ❌ CSV测试失败: {e}")
    finally:
        if os.path.exists(csv_file):
            os.remove(csv_file)
    
    print()
    print("="*80)
    print("🎉 优化功能演示完成！")
    print()
    print("✅ 已实现的优化功能:")
    print("   • 11位纯数字预检查和特殊处理")
    print("   • 金额字段2位小数精度保持")
    print("   • CSV输出完全防止科学计数法")
    print("   • '其他站点信息'字段始终位于最后")
    print("   • 字段值映射完全基于字段名，消除错位")
    print("   • 原始'站点地址'字段完整保留")
    print("   • 两阶段扫描确保字段完整性")
    print("   • 强化的字符串格式保护")
    print()
    print("🚀 优化后的DBF合并工具已准备就绪，可以安全处理生产数据！")

if __name__ == "__main__":
    demonstrate_optimized_features()
