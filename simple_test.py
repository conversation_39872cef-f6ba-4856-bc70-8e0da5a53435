"""
简化测试脚本 - 验证基本功能
"""

import pandas as pd
import os
import tempfile

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本功能...")
    
    # 创建简单测试数据
    test_data = [
        {
            '客户编号': 'CUST001',
            '客户姓名': '张三',
            '资产账户': 'ACC001',
            'MAC': 'AA:BB:CC:DD:EE:01',
            '发生金额': '100.00',
            '后资金额': '1000.00'
        },
        {
            '客户编号': 'CUST002',
            '客户姓名': '张三',  # 同名不同人
            '资产账户': 'ACC002',
            'MAC': 'AA-BB-CC-DD-EE-01',  # 相同MAC不同格式
            '发生金额': '200.00',
            '后资金额': '2000.00'
        }
    ]
    
    df = pd.DataFrame(test_data)
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig') as f:
        df.to_csv(f.name, index=False, encoding='utf-8-sig')
        temp_file = f.name
    
    try:
        print(f"📂 测试文件: {temp_file}")
        print(f"📊 数据行数: {len(df)}")
        
        # 导入并测试
        from merge_dbf import SameSourceDataAnalyzer
        
        analyzer = SameSourceDataAnalyzer(temp_file)
        print("✅ 分析器创建成功")
        
        # 测试数据加载
        analyzer._load_data()
        print("✅ 数据加载成功")
        
        # 测试客户身份识别策略分析
        strategy = analyzer._analyze_customer_identity_strategy()
        print(f"✅ 客户身份识别策略: {strategy}")
        
        # 测试客户身份构建
        identity1 = analyzer._build_customer_identity(0)
        identity2 = analyzer._build_customer_identity(1)
        print(f"✅ 客户身份1: {identity1}")
        print(f"✅ 客户身份2: {identity2}")
        
        if identity1 != identity2:
            print("✅ 同名不同客户正确识别为不同身份")
        else:
            print("❌ 同名不同客户未能正确区分")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if os.path.exists(temp_file):
            os.unlink(temp_file)

if __name__ == "__main__":
    print("=" * 50)
    print("简化功能测试")
    print("=" * 50)
    
    success = test_basic_functionality()
    
    if success:
        print("\n🎉 基本功能测试通过！")
    else:
        print("\n❌ 基本功能测试失败！")
