#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
极端数值格式测试 - 确保在所有情况下都不会出现科学计数法
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from merge_dbf import DBFMergerPro
import pandas as pd

def test_extreme_numeric_cases():
    """测试极端数值情况"""
    
    print("🧪 测试极端数值格式处理...\n")
    
    merger = DBFMergerPro()
    
    # 极端数值测试用例
    extreme_cases = [
        {
            "name": "超长整数",
            "input": "PC;IIP=*******;HD=12345678901234567890123456789012345678901234567890;CPU=98765432109876543210987654321098765432109876543210",
            "critical_fields": ["HD", "CPU"]
        },
        {
            "name": "大整数（可能触发科学计数法）",
            "input": "MA;IIP=*******;IMEI=1234567890123456789;RMPN=12345678901234567890;ICCID=123456789012345678901234567890",
            "critical_fields": ["IMEI", "RMPN", "ICCID"]
        },
        {
            "name": "以零开头的数字",
            "input": "BK;IIP=*******;MAC=001122334455;IPORT=0080;HD=000123456789012345;VER=001.002.003",
            "critical_fields": ["MAC", "IPORT", "HD", "VER"]
        },
        {
            "name": "混合数字和字母",
            "input": "MI;IIP=*******;IDFV=1A2B3C4D-5E6F-7890-ABCD-123456789012;MAC=AA1BB2CC3DD4;OSV=Android123",
            "critical_fields": ["IDFV", "MAC", "OSV"]
        },
        {
            "name": "纯数字字符串",
            "input": "PC;IIP=*******;NUM1=1000000000000000000;NUM2=2000000000000000000;NUM3=3000000000000000000",
            "critical_fields": ["NUM1", "NUM2", "NUM3"]
        }
    ]
    
    all_data = []
    
    for i, test_case in enumerate(extreme_cases, 1):
        print(f"测试用例 {i}: {test_case['name']}")
        print(f"输入: {test_case['input']}")
        
        # 解析数据
        parsed = merger._recursive_split(test_case['input'])
        parsed['测试用例'] = test_case['name']
        all_data.append(parsed)
        
        print("关键字段检查:")
        for field in test_case['critical_fields']:
            if field in parsed:
                value = parsed[field]
                print(f"  {field}: '{value}' (长度: {len(str(value))})")
                
                # 检查是否为字符串类型
                if not isinstance(value, str):
                    print(f"    ⚠️ 警告: 不是字符串类型，而是 {type(value).__name__}")
                else:
                    print(f"    ✅ 字符串类型正确")
        
        print("-" * 60)
    
    # 创建DataFrame并测试CSV输出
    print("\n📊 创建DataFrame并测试CSV输出...")
    
    # 设置merger的active_columns以包含所有解析出的字段
    all_fields = set()
    for data in all_data:
        all_fields.update(data.keys())
    merger.active_columns = all_fields - {'测试用例'}  # 排除测试用例字段
    
    df = pd.DataFrame(all_data)
    
    print("DataFrame数据类型检查:")
    for col in df.columns:
        if col != '测试用例':
            dtype = df[col].dtype
            print(f"  {col}: {dtype}")
            
            # 检查是否有非字符串的数值
            non_null_values = df[col].dropna()
            if len(non_null_values) > 0:
                sample_value = non_null_values.iloc[0]
                print(f"    示例值: '{sample_value}' (类型: {type(sample_value).__name__})")
    
    # 测试CSV写入
    csv_file = "test_extreme_numeric.csv"
    try:
        print(f"\n💾 写入CSV文件: {csv_file}")
        merger.output_path = csv_file  # 设置输出路径
        merger._write_csv(df)  # 使用merger的方法来测试
        
        # 读取并检查CSV内容
        print("📖 检查CSV文件内容...")
        with open(csv_file, 'r', encoding='utf_8_sig') as f:
            content = f.read()
        
        # 检查是否包含科学计数法
        scientific_patterns = ['e+', 'e-', 'E+', 'E-']
        found_scientific = False
        for pattern in scientific_patterns:
            if pattern in content:
                print(f"❌ 发现科学计数法格式: {pattern}")
                found_scientific = True
        
        if not found_scientific:
            print("✅ 未发现科学计数法格式")
        
        # 显示CSV内容的前几行
        print("\nCSV文件内容预览:")
        lines = content.split('\n')[:6]  # 显示前6行
        for i, line in enumerate(lines):
            if line.strip():
                print(f"  行{i+1}: {line}")
        
        # 检查特定的长数字是否保持完整
        test_numbers = [
            "12345678901234567890123456789012345678901234567890",
            "98765432109876543210987654321098765432109876543210",
            "1234567890123456789",
            "000123456789012345"
        ]
        
        print("\n🔍 检查特定长数字是否完整保存:")
        for num in test_numbers:
            if num in content:
                print(f"  ✅ 找到完整数字: {num}")
            else:
                print(f"  ❌ 数字可能被修改: {num}")
        
        return not found_scientific
        
    except Exception as e:
        print(f"❌ CSV测试失败: {e}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists(csv_file):
            os.remove(csv_file)
            print(f"🗑️ 已清理测试文件: {csv_file}")

def test_field_order_with_extreme_values():
    """测试字段顺序独立性与极端数值的组合"""
    
    print("\n🧪 测试字段顺序独立性 + 极端数值...\n")
    
    merger = DBFMergerPro()
    
    # 相同的极端数值，不同的字段顺序
    test_cases = [
        {
            "name": "顺序A",
            "input": "PC;HD=12345678901234567890;CPU=98765432109876543210;IIP=*******;MAC=AABBCCDDEEFF",
        },
        {
            "name": "顺序B (重新排列)",
            "input": "PC;IIP=*******;MAC=AABBCCDDEEFF;HD=12345678901234567890;CPU=98765432109876543210",
        }
    ]
    
    results = []
    for test_case in test_cases:
        print(f"测试: {test_case['name']}")
        print(f"输入: {test_case['input']}")
        
        result = merger._recursive_split(test_case['input'])
        results.append(result)
        
        print(f"HD值: '{result.get('HD', 'N/A')}'")
        print(f"CPU值: '{result.get('CPU', 'N/A')}'")
        print()
    
    # 验证两个结果中的关键字段值是否相同
    if len(results) == 2:
        fields_to_check = ['设备类型', 'HD', 'CPU', 'IIP', 'MAC']
        all_match = True
        
        for field in fields_to_check:
            val1 = results[0].get(field, '')
            val2 = results[1].get(field, '')
            if val1 == val2:
                print(f"✅ {field}: 两个顺序结果一致 ('{val1}')")
            else:
                print(f"❌ {field}: 结果不一致 ('{val1}' vs '{val2}')")
                all_match = False
        
        return all_match
    
    return False

def main():
    """主测试函数"""
    print("🚨 极端数值格式测试开始\n")
    
    # 测试1: 极端数值格式
    extreme_test_passed = test_extreme_numeric_cases()
    
    # 测试2: 字段顺序 + 极端数值
    order_extreme_test_passed = test_field_order_with_extreme_values()
    
    print("\n" + "="*80)
    print("📊 极端测试结果总结:")
    print(f"极端数值格式测试: {'✅ 通过' if extreme_test_passed else '❌ 失败'}")
    print(f"字段顺序+极端数值测试: {'✅ 通过' if order_extreme_test_passed else '❌ 失败'}")
    
    if extreme_test_passed and order_extreme_test_passed:
        print("\n🎉 所有极端情况测试通过！")
        print("✅ 数值格式完全受保护，不会出现科学计数法")
        print("✅ 字段解析完全基于字段名，不依赖位置")
    else:
        print("\n⚠️ 存在需要进一步优化的问题！")
    
    return extreme_test_passed and order_extreme_test_passed

if __name__ == "__main__":
    main()
