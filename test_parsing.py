#!/usr/bin/env python3
"""
测试站点地址解析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from merge_dbf import DBFMergerPro

def test_parsing():
    """测试解析功能"""
    merger = DBFMergerPro()
    
    # 测试数据
    test_data = "MA;IIP=*******;IPORT=0088;LIP=***********;MAC=ABCDABCD;RMPN=15600000000;UMPN=18888888888;ICCID=55667788;OSV=ANDROID13;IMSI=NA@cbbPlus;*******"
    
    print("🧪 测试站点地址解析功能")
    print(f"原始数据: {test_data}")
    print("\n" + "="*80)
    
    # 执行解析
    result = merger._recursive_split(test_data)
    
    print("📊 解析结果:")
    print(f"总共解析出 {len(result)} 个字段:")
    
    # 按类型分组显示
    boolean_fields = []
    value_fields = []
    
    for field_name, field_value in result.items():
        if isinstance(field_value, bool):
            boolean_fields.append(field_name)
        else:
            value_fields.append((field_name, field_value))
    
    print(f"\n🔢 键值对字段 ({len(value_fields)} 个):")
    for key, value in value_fields:
        print(f"  {key} = {value}")
    
    print(f"\n✅ 布尔标记字段 ({len(boolean_fields)} 个):")
    for field in boolean_fields:
        print(f"  {field} = True")
    
    # 验证期望的字段是否都存在
    expected_keys = ['MA', 'IIP', 'IPORT', 'LIP', 'MAC', 'RMPN', 'UMPN', 'ICCID', 'OSV', 'IMSI', 'cbbPlus', '*******']
    expected_full_pairs = ['IIP=*******', 'IPORT=0088', 'LIP=***********', 'MAC=ABCDABCD', 'RMPN=15600000000', 'UMPN=18888888888', 'ICCID=55667788', 'OSV=ANDROID13', 'IMSI=NA']
    
    print(f"\n🎯 验证期望字段:")
    
    missing_keys = []
    for key in expected_keys:
        if key in result:
            print(f"  ✅ {key}: 存在")
        else:
            print(f"  ❌ {key}: 缺失")
            missing_keys.append(key)
    
    missing_pairs = []
    for pair in expected_full_pairs:
        if pair in result:
            print(f"  ✅ {pair}: 存在")
        else:
            print(f"  ❌ {pair}: 缺失")
            missing_pairs.append(pair)
    
    # 总结
    print(f"\n📋 测试总结:")
    if not missing_keys and not missing_pairs:
        print("  🎉 所有期望字段都正确解析！")
        return True
    else:
        print(f"  ⚠️ 发现问题:")
        if missing_keys:
            print(f"    缺失键名: {missing_keys}")
        if missing_pairs:
            print(f"    缺失键值对: {missing_pairs}")
        return False

def test_boolean_field_detection():
    """测试布尔字段检测功能"""
    merger = DBFMergerPro()
    
    print("\n🔍 测试布尔字段检测:")
    
    test_fields = [
        ('MA', True),
        ('IIP', False),
        ('IIP=*******', True),
        ('cbbPlus', True),
        ('*******', True),
        ('ANDROID13', False)
    ]
    
    for field, expected in test_fields:
        result = merger._is_boolean_field(field)
        status = "✅" if result == expected else "❌"
        print(f"  {status} {field}: {result} (期望: {expected})")

if __name__ == "__main__":
    success = test_parsing()
    test_boolean_field_detection()
    
    if success:
        print("\n🎊 所有测试通过！解析功能工作正常。")
    else:
        print("\n💥 测试失败！需要进一步调试。")
