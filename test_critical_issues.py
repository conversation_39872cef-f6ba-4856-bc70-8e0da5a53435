#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试关键问题：字段顺序依赖性和科学计数法问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from merge_dbf import DBFMergerPro
import pandas as pd

def test_field_order_independence():
    """测试字段顺序独立性 - 确保解析基于字段名而非位置"""
    
    print("🧪 测试字段顺序独立性...\n")
    
    merger = DBFMergerPro()
    
    # 测试用例：相同字段，不同顺序
    test_cases = [
        {
            "name": "顺序1: MA设备",
            "input": "MA;IIP=*******;MAC=ABCDEFG;UMPN=15500001123;IMEI=123456789012345;RMPN=13800138000",
            "expected": {
                "设备类型": "MA",
                "IIP": "*******",
                "MAC": "ABCDEFG", 
                "UMPN": "15500001123",
                "IMEI": "123456789012345",
                "RMPN": "13800138000"
            }
        },
        {
            "name": "顺序2: MA设备 (字段重新排列)",
            "input": "MA;RMPN=13800138000;IIP=*******;IMEI=123456789012345;UMPN=15500001123;MAC=ABCDEFG",
            "expected": {
                "设备类型": "MA",
                "IIP": "*******",
                "MAC": "ABCDEFG",
                "UMPN": "15500001123", 
                "IMEI": "123456789012345",
                "RMPN": "13800138000"
            }
        },
        {
            "name": "顺序3: PC设备",
            "input": "PC;IIP=*******;CPU=541561546456465;MAC=BCEDFGH;HD=987654321098765;VER=2.1.0",
            "expected": {
                "设备类型": "PC",
                "IIP": "*******",
                "CPU": "541561546456465",
                "MAC": "BCEDFGH",
                "HD": "987654321098765",
                "VER": "2.1.0"
            }
        },
        {
            "name": "顺序4: PC设备 (字段重新排列)",
            "input": "PC;HD=987654321098765;MAC=BCEDFGH;VER=2.1.0;CPU=541561546456465;IIP=*******",
            "expected": {
                "设备类型": "PC",
                "IIP": "*******",
                "CPU": "541561546456465", 
                "MAC": "BCEDFGH",
                "HD": "987654321098765",
                "VER": "2.1.0"
            }
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试用例 {i}: {test_case['name']}")
        print(f"输入: {test_case['input']}")
        
        # 执行解析
        result = merger._recursive_split(test_case['input'])
        
        print(f"解析结果: {result}")
        print(f"期望结果: {test_case['expected']}")
        
        # 验证每个字段
        test_passed = True
        for field, expected_value in test_case['expected'].items():
            if field not in result:
                print(f"❌ 缺失字段: {field}")
                test_passed = False
                all_passed = False
            elif result[field] != expected_value:
                print(f"❌ 字段 '{field}' 值不匹配:")
                print(f"   期望: '{expected_value}'")
                print(f"   实际: '{result[field]}'")
                test_passed = False
                all_passed = False
        
        # 检查是否有多余字段
        extra_fields = set(result.keys()) - set(test_case['expected'].keys())
        if extra_fields:
            print(f"⚠️ 多余字段: {extra_fields}")
        
        if test_passed:
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")
        
        print("-" * 60)
    
    return all_passed

def test_numeric_formatting():
    """测试数值格式保持 - 确保长数字不被转换为科学计数法"""
    
    print("\n🧪 测试数值格式保持...\n")
    
    merger = DBFMergerPro()
    
    # 包含长数字的测试用例
    test_cases = [
        {
            "name": "长数字测试",
            "input": "PC;IIP=***********;HD=123456789012345678901234567890;CPU=987654321098765432109876543210;IMEI=123456789012345;RMPN=13800138000",
            "long_number_fields": ["HD", "CPU", "IMEI", "RMPN"]
        },
        {
            "name": "手机号码测试", 
            "input": "MA;IIP=********;RMPN=15500001123;UMPN=13912345678;IMEI=860123456789012;ICCID=89860123456789012345",
            "long_number_fields": ["RMPN", "UMPN", "IMEI", "ICCID"]
        }
    ]
    
    print("测试解析结果中的数值格式:")
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {test_case['name']}")
        print(f"输入: {test_case['input']}")
        
        result = merger._recursive_split(test_case['input'])
        
        print("长数字字段检查:")
        for field in test_case['long_number_fields']:
            if field in result:
                value = result[field]
                print(f"  {field}: '{value}' (类型: {type(value).__name__})")
                
                # 检查是否包含科学计数法标记
                if 'e' in str(value).lower() or 'E' in str(value):
                    print(f"    ⚠️ 可能包含科学计数法格式")
                else:
                    print(f"    ✅ 格式正常")
    
    # 测试DataFrame和CSV输出
    print("\n测试DataFrame和CSV输出格式:")
    
    # 创建测试数据
    test_data = []
    for test_case in test_cases:
        parsed = merger._recursive_split(test_case['input'])
        parsed['测试名称'] = test_case['name']
        test_data.append(parsed)
    
    # 创建DataFrame
    df = pd.DataFrame(test_data)
    
    print("DataFrame中的数值:")
    for col in df.columns:
        if col in ['HD', 'CPU', 'IMEI', 'RMPN', 'UMPN', 'ICCID']:
            print(f"  {col}:")
            for idx, val in df[col].items():
                if pd.notna(val):
                    print(f"    行{idx}: '{val}' (类型: {type(val).__name__})")
    
    # 测试CSV输出
    csv_file = "test_numeric_output.csv"
    try:
        df.to_csv(csv_file, index=False, encoding='utf_8_sig')
        print(f"\n✅ CSV文件已生成: {csv_file}")
        
        # 读取CSV文件检查格式
        with open(csv_file, 'r', encoding='utf_8_sig') as f:
            content = f.read()
            print("CSV文件内容预览:")
            lines = content.split('\n')[:5]  # 显示前5行
            for line in lines:
                print(f"  {line}")
                
        # 检查是否包含科学计数法
        if 'e+' in content.lower() or 'e-' in content.lower():
            print("❌ CSV文件包含科学计数法格式")
            return False
        else:
            print("✅ CSV文件数值格式正常")
            
    except Exception as e:
        print(f"❌ CSV测试失败: {e}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists(csv_file):
            os.remove(csv_file)
    
    return True

def main():
    """主测试函数"""
    print("🚨 关键问题测试开始\n")
    
    # 测试1: 字段顺序独立性
    order_test_passed = test_field_order_independence()
    
    # 测试2: 数值格式保持
    numeric_test_passed = test_numeric_formatting()
    
    print("\n" + "="*80)
    print("📊 测试结果总结:")
    print(f"字段顺序独立性测试: {'✅ 通过' if order_test_passed else '❌ 失败'}")
    print(f"数值格式保持测试: {'✅ 通过' if numeric_test_passed else '❌ 失败'}")
    
    if order_test_passed and numeric_test_passed:
        print("\n🎉 所有关键问题测试通过！")
    else:
        print("\n⚠️ 存在需要修复的问题！")
    
    return order_test_passed and numeric_test_passed

if __name__ == "__main__":
    main()
