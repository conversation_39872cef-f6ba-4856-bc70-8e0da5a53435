"""
测试DBF智能识别同源数据分析功能的客户身份识别修复和格式选择优化
验证基于客户编号的同源数据识别逻辑和CSV/Excel格式选择机制
"""

import pandas as pd
import os
import tempfile
from merge_dbf import SameSourceDataAnalyzer

def create_customer_id_test_data():
    """创建基于客户编号的测试数据"""
    print("📝 创建客户编号测试数据...")
    
    test_data = [
        # 场景1：客户001的多条记录（同一客户编号，不应识别为同源数据）
        {
            '客户编号': 'CUST001',
            '客户姓名': '张三',
            '资产账户': 'ACC001',
            '站点地址': 'MA;IIP=***********;MAC=AA:BB:CC:DD:EE:01;IMEI=123456789012345',
            'IIP': '***********',
            'MAC': 'AA:BB:CC:DD:EE:01',
            'IMEI': '123456789012345',
            'TEL': '17711102220',
            '发生金额': '100.00',
            '后资金额': '1000.00'
        },
        {
            '客户编号': 'CUST001',  # 相同客户编号
            '客户姓名': '张三',
            '资产账户': 'ACC001',
            '站点地址': 'MA;IIP=***********;MAC=AA:BB:CC:DD:EE:01;IMEI=123456789012345',
            'IIP': '***********',
            'MAC': 'AA:BB:CC:DD:EE:01',
            'IMEI': '123456789012345',
            'TEL': '17711102220',
            '发生金额': '150.00',
            '后资金额': '1150.00'
        },
        {
            '客户编号': 'CUST001',  # 相同客户编号的第三条记录
            '客户姓名': '张三',
            '资产账户': 'ACC001',
            '站点地址': 'MA;IIP=***********;MAC=AA:BB:CC:DD:EE:01',
            'IIP': '***********',
            'MAC': 'AA:BB:CC:DD:EE:01',
            'IMEI': '123456789012345',
            'TEL': '17711102220',
            '发生金额': '200.00',
            '后资金额': '1200.00'
        },
        
        # 场景2：客户002使用与客户001相同的设备（不同客户编号，应识别为同源数据）
        {
            '客户编号': 'CUST002',  # 不同客户编号
            '客户姓名': '李四',
            '资产账户': 'ACC002',
            '站点地址': 'MA;IIP=***********;MAC=AA-BB-CC-DD-EE-01;IMEI=123456789012345',
            'IIP': '***********',   # 与CUST001相同的IP
            'MAC': 'AA-BB-CC-DD-EE-01',  # 与CUST001相同的MAC（不同格式）
            'IMEI': '123456789012345',   # 与CUST001相同的IMEI
            'TEL': '13800138002',
            '发生金额': '300.00',
            '后资金额': '3000.00'
        },
        
        # 场景3：客户003使用独立设备
        {
            '客户编号': 'CUST003',
            '客户姓名': '王五',
            '资产账户': 'ACC003',
            '站点地址': 'PC;IMEI=987654321098765;MAC=BB:CC:DD:EE:FF:02',
            'IMEI': '987654321098765',
            'MAC': 'BB:CC:DD:EE:FF:02',
            'TEL': '13900139001',
            '发生金额': '400.00',
            '后资金额': '4000.00'
        },
        
        # 场景4：客户004使用与客户003相同的IMEI（不同客户编号，应识别为同源数据）
        {
            '客户编号': 'CUST004',  # 不同客户编号
            '客户姓名': '赵六',
            '资产账户': 'ACC004',
            '站点地址': 'PC;IMEI=\'987654321098765;MAC=CC:DD:EE:FF:AA:03',
            'IMEI': "'987654321098765",  # 与CUST003相同的IMEI（包含引号）
            'MAC': 'CC:DD:EE:FF:AA:03',
            'TEL': '13900139002',
            '发生金额': '500.00',
            '后资金额': '5000.00'
        }
    ]
    
    return pd.DataFrame(test_data)

def create_fallback_test_data():
    """创建没有客户编号的测试数据（备用策略）"""
    print("📝 创建备用策略测试数据...")
    
    test_data = [
        # 只有客户姓名和资产账户的数据
        {
            '客户姓名': '张三',
            '资产账户': 'ACC001',
            'MAC': 'AA:BB:CC:DD:EE:01',
            'IMEI': '123456789012345',
            '发生金额': '100.00',
            '后资金额': '1000.00'
        },
        {
            '客户姓名': '张三',      # 相同姓名
            '资产账户': 'ACC002',    # 不同账户（应识别为不同客户）
            'MAC': 'AA-BB-CC-DD-EE-01',  # 相同MAC（应识别为同源数据）
            'IMEI': '123456789012345',   # 相同IMEI
            '发生金额': '200.00',
            '后资金额': '2000.00'
        }
    ]
    
    return pd.DataFrame(test_data)

def test_customer_id_logic():
    """测试基于客户编号的同源数据识别逻辑"""
    print("\n🧪 测试客户编号同源数据识别逻辑")
    print("-" * 60)
    
    # 创建测试数据
    test_df = create_customer_id_test_data()
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig') as f:
        test_df.to_csv(f.name, index=False, encoding='utf-8-sig')
        temp_file = f.name
    
    try:
        print(f"📂 测试文件: {os.path.basename(temp_file)}")
        print(f"📊 测试数据: {len(test_df)} 行记录")
        print(f"👥 客户分布:")
        customer_counts = test_df['客户编号'].value_counts()
        for customer_id, count in customer_counts.items():
            print(f"   - {customer_id}: {count}条记录")
        
        # 执行分析（使用Excel格式）
        analyzer = SameSourceDataAnalyzer(temp_file, output_format='excel')
        analyzer.run_analysis()
        
        # 验证结果
        print(f"\n📋 客户身份识别验证:")
        print(f"   - 识别策略: {analyzer.customer_identity_strategy}")
        print(f"   - 使用字段: {analyzer.available_customer_fields}")
        
        # 验证同源数据识别结果
        # 预期结果：只有不同客户编号使用相同设备时才识别为同源数据
        expected_same_source = {
            'IIP': 1,    # CUST001和CUST002共享***********
            'MAC': 1,    # CUST001和CUST002共享MAC
            'IMEI': 2,   # CUST001和CUST002共享IMEI + CUST003和CUST004共享IMEI
            'TEL': 0,    # 没有不同客户编号共享TEL
        }
        
        verification_passed = True
        
        print(f"\n📊 同源数据识别结果:")
        for field, expected_count in expected_same_source.items():
            actual_count = len(analyzer.duplicate_groups.get(field, []))
            status = "✅" if actual_count == expected_count else "❌"
            if actual_count != expected_count:
                verification_passed = False
            
            print(f"   {status} {field}: 期望 {expected_count} 组，实际 {actual_count} 组")
            
            if field in analyzer.duplicate_groups:
                for i, group in enumerate(analyzer.duplicate_groups[field], 1):
                    customer_count = group['customer_count']
                    record_count = group['record_count']
                    clean_value = group['clean_value']
                    print(f"     组{i}: 值'{clean_value}' → {customer_count}个客户，{record_count}条记录")
        
        # 验证CUST001的多条记录没有被误识别
        print(f"\n🔍 验证同一客户多条记录处理:")
        cust001_misidentified = False
        for field, groups in analyzer.duplicate_groups.items():
            for group in groups:
                # 检查是否有只包含一个客户编号的组
                customer_ids = set()
                for idx in group['row_indices']:
                    customer_id = analyzer.data.loc[idx, '客户编号']
                    customer_ids.add(customer_id)
                
                if len(customer_ids) == 1 and 'CUST001' in customer_ids:
                    cust001_misidentified = True
                    print(f"   ❌ 错误：CUST001的记录在{field}字段被误识别为同源数据")
        
        if not cust001_misidentified:
            print(f"   ✅ 正确：CUST001的多条记录未被误识别为同源数据")
        else:
            verification_passed = False
        
        return verification_passed, analyzer.output_file
        
    except Exception as e:
        print(f"❌ 测试过程出错：{str(e)}")
        import traceback
        traceback.print_exc()
        return False, None
    finally:
        # 清理临时文件
        if os.path.exists(temp_file):
            os.unlink(temp_file)

def test_format_selection():
    """测试输出格式选择机制"""
    print("\n🧪 测试输出格式选择机制")
    print("-" * 60)
    
    # 创建测试数据
    test_df = create_customer_id_test_data()
    
    # 测试Excel格式
    print("📊 测试Excel格式输出:")
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig') as f:
        test_df.to_csv(f.name, index=False, encoding='utf-8-sig')
        temp_file = f.name
    
    try:
        analyzer_excel = SameSourceDataAnalyzer(temp_file, output_format='excel')
        analyzer_excel.run_analysis()
        
        excel_exists = os.path.exists(analyzer_excel.output_file)
        excel_size = os.path.getsize(analyzer_excel.output_file) if excel_exists else 0
        
        print(f"   ✅ Excel文件生成: {excel_exists}")
        print(f"   📊 Excel文件大小: {excel_size:,} 字节 ({excel_size / 1024:.2f} KB)")
        
    except Exception as e:
        print(f"   ❌ Excel格式测试失败: {str(e)}")
        excel_exists = False
        excel_size = 0
    finally:
        if os.path.exists(temp_file):
            os.unlink(temp_file)
    
    # 测试CSV格式
    print("\n📊 测试CSV格式输出:")
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig') as f:
        test_df.to_csv(f.name, index=False, encoding='utf-8-sig')
        temp_file = f.name
    
    try:
        analyzer_csv = SameSourceDataAnalyzer(temp_file, output_format='csv')
        analyzer_csv.run_analysis()
        
        # 检查CSV文件
        base_name = analyzer_csv.output_file
        csv_files = [
            f"{base_name}_分析概览.csv",
            f"{base_name}_数据验证结果.csv",
            f"{base_name}_同源数据汇总.csv"
        ]
        
        csv_total_size = 0
        csv_files_exist = 0
        for csv_file in csv_files:
            if os.path.exists(csv_file):
                csv_files_exist += 1
                csv_size = os.path.getsize(csv_file)
                csv_total_size += csv_size
                print(f"   ✅ {os.path.basename(csv_file)}: {csv_size:,} 字节")
        
        print(f"   📊 CSV文件总数: {csv_files_exist}/{len(csv_files)}")
        print(f"   📊 CSV总大小: {csv_total_size:,} 字节 ({csv_total_size / 1024:.2f} KB)")
        
    except Exception as e:
        print(f"   ❌ CSV格式测试失败: {str(e)}")
        csv_total_size = 0
        csv_files_exist = 0
    finally:
        if os.path.exists(temp_file):
            os.unlink(temp_file)
    
    # 格式对比
    print(f"\n📈 格式对比结果:")
    if excel_exists and csv_files_exist > 0:
        size_ratio = csv_total_size / excel_size if excel_size > 0 else 0
        print(f"   📊 大小对比: CSV是Excel的 {size_ratio:.1%}")
        print(f"   ✅ 两种格式都能正常生成")
        return True
    else:
        print(f"   ❌ 格式测试未完全通过")
        return False

def main():
    """主测试函数"""
    print("=" * 80)
    print("DBF智能识别同源数据分析功能 - 客户编号逻辑和格式选择修复验证")
    print("=" * 80)
    
    # 测试客户编号逻辑
    logic_test_passed, result_file = test_customer_id_logic()
    
    # 测试格式选择
    format_test_passed = test_format_selection()
    
    # 总结测试结果
    print(f"\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    if logic_test_passed and format_test_passed:
        print("🎉 所有测试通过！修复完全成功！")
        print("✅ 客户编号同源数据识别逻辑正确")
        print("✅ 同一客户多条记录处理正确")
        print("✅ CSV和Excel格式都能正常生成")
        print("✅ 格式选择机制工作正常")
        
        print(f"\n💡 修复成果总结:")
        print(f"   1. 简化客户身份识别：仅使用客户编号作为主要标识")
        print(f"   2. 正确的同源数据定义：相同设备标识对应多个不同客户编号")
        print(f"   3. 智能格式选择：根据文件大小自动推荐最佳格式")
        print(f"   4. 完整的CSV格式支持：保持与Excel相同的功能完整性")
        print(f"   5. 大文件处理优化：提供性能更好的CSV格式选项")
        
        if result_file and os.path.exists(result_file):
            print(f"\n📄 测试报告文件: {result_file}")
        
    else:
        print("❌ 测试失败，需要进一步修复")
        if not logic_test_passed:
            print("❌ 客户编号同源数据识别逻辑存在问题")
        if not format_test_passed:
            print("❌ 格式选择机制存在问题")

if __name__ == "__main__":
    main()
