#!/usr/bin/env python3
"""
测试增强功能的脚本
验证四个主要增强功能：
1. PyQt5 GUI集成
2. 跳转分析功能
3. 改进的同源数据结果可读性
4. 排除常见误报值
"""

import sys
import os
import pandas as pd
import tempfile
from merge_dbf import (
    DBFMergerPro, 
    SameSourceDataAnalyzer, 
    FALSE_POSITIVE_VALUES,
    DUPLICATE_CHECK_FIELDS
)

def test_false_positive_filtering():
    """测试误报值过滤功能"""
    print("🧪 测试误报值过滤功能...")

    # 创建临时文件用于初始化分析器
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    temp_file.write("客户编号,MAC\nCUST001,AA:BB:CC:DD:EE:FF\n")
    temp_file.close()

    try:
        analyzer = SameSourceDataAnalyzer(temp_file.name)

        # 测试各种误报值
        test_cases = [
            ("IMSI", "NA@储宝宝Plus", True),
            ("IMSI", "NA@TYPE=GM", True),
            ("IMSI", "6553565535", True),
            ("IMSI", "460001234567890", False),  # 正常IMSI
            ("MAC", "02:00:00:00:00:00", True),
            ("MAC", "00", True),
            ("MAC", "AA:BB:CC:DD:EE:FF", False),  # 正常MAC
            ("CPU", "BFEBFBFF000306C3", True),
            ("CPU", "BFEBFBFF000906E9", False),  # 正常CPU
        ]

        for field, value, expected_is_false_positive in test_cases:
            result = analyzer._is_false_positive_value(field, value)
            status = "✅" if result == expected_is_false_positive else "❌"
            print(f"   {status} {field}='{value}' -> 误报值: {result} (期望: {expected_is_false_positive})")

        print("✅ 误报值过滤功能测试完成\n")

    finally:
        try:
            os.unlink(temp_file.name)
        except:
            pass

def test_clean_value_comparison():
    """测试值清理和比较功能"""
    print("🧪 测试值清理和比较功能...")

    # 创建临时文件用于初始化分析器
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    temp_file.write("客户编号,MAC\nCUST001,AA:BB:CC:DD:EE:FF\n")
    temp_file.close()

    try:
        analyzer = SameSourceDataAnalyzer(temp_file.name)

        test_cases = [
            ("AA:BB:CC:DD:EE:FF", "AABBCCDDEEFF"),
            ("02-00-00-00-00-00", "020000000000"),
            ("192.168.1.1", "19216811"),
            ("IMSI@TYPE=GM", "IMSITYPEGM"),
            ("CPU-ID_123", "CPUID123"),
        ]

        for original, expected in test_cases:
            result = analyzer._clean_value_for_comparison(original)
            status = "✅" if result == expected else "❌"
            print(f"   {status} '{original}' -> '{result}' (期望: '{expected}')")

        print("✅ 值清理功能测试完成\n")

    finally:
        try:
            os.unlink(temp_file.name)
        except:
            pass

def create_test_data():
    """创建测试数据"""
    print("📊 创建测试数据...")
    
    # 创建包含同源数据和误报值的测试数据
    test_data = [
        # 正常的同源数据（同一MAC被不同客户使用）
        {"客户编号": "CUST001", "客户姓名": "张三", "MAC": "AA:BB:CC:DD:EE:FF", "IIP": "*************"},
        {"客户编号": "CUST002", "客户姓名": "李四", "MAC": "AA:BB:CC:DD:EE:FF", "IIP": "*************"},
        
        # 误报值（应该被排除）
        {"客户编号": "CUST003", "客户姓名": "王五", "MAC": "02:00:00:00:00:00", "IIP": "*************"},
        {"客户编号": "CUST004", "客户姓名": "赵六", "MAC": "02:00:00:00:00:00", "IIP": "*************"},
        {"客户编号": "CUST005", "客户姓名": "钱七", "IMSI": "NA@储宝宝Plus", "IIP": "*************"},
        {"客户编号": "CUST006", "客户姓名": "孙八", "IMSI": "NA@储宝宝Plus", "IIP": "*************"},
        
        # 正常的独有数据
        {"客户编号": "CUST007", "客户姓名": "周九", "MAC": "11:22:33:44:55:66", "IIP": "*************"},
        {"客户编号": "CUST008", "客户姓名": "吴十", "MAC": "77:88:99:AA:BB:CC", "IIP": "*************"},
    ]
    
    df = pd.DataFrame(test_data)
    
    # 保存到临时文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    df.to_csv(temp_file.name, index=False)
    temp_file.close()
    
    print(f"✅ 测试数据已创建: {temp_file.name}")
    print(f"   - 总记录数: {len(df)}")
    print(f"   - 包含正常同源数据: MAC 'AA:BB:CC:DD:EE:FF' 被2个客户使用")
    print(f"   - 包含误报值: MAC '02:00:00:00:00:00' 和 IMSI 'NA@储宝宝Plus'")
    
    return temp_file.name

def test_same_source_analysis():
    """测试同源数据分析功能"""
    print("\n🧪 测试同源数据分析功能...")
    
    # 创建测试数据
    test_file = create_test_data()
    
    try:
        # 创建分析器
        analyzer = SameSourceDataAnalyzer(test_file, output_format='csv')
        
        # 运行分析
        analyzer.run_analysis()
        
        print("✅ 同源数据分析测试完成")
        
        # 检查生成的文件
        base_name = analyzer.output_file
        expected_files = [
            f"{base_name}_分析概览.csv",
            f"{base_name}_数据验证结果.csv"
        ]
        
        if analyzer.duplicate_groups:
            expected_files.append(f"{base_name}_同源数据汇总.csv")
            # 检查字段特定文件
            for field in analyzer.duplicate_groups.keys():
                expected_files.append(f"{base_name}_{field}字段同源数据.csv")
        
        print("📄 生成的分析文件:")
        for file_path in expected_files:
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"   ✅ {os.path.basename(file_path)} ({file_size} 字节)")
            else:
                print(f"   ❌ {os.path.basename(file_path)} (未找到)")
        
    except Exception as e:
        print(f"❌ 同源数据分析测试失败: {str(e)}")
    finally:
        # 清理测试文件
        try:
            os.unlink(test_file)
        except:
            pass

def test_configuration():
    """测试配置和常量"""
    print("🧪 测试配置和常量...")
    
    # 检查误报值配置
    print("🚫 误报值配置:")
    for field, values in FALSE_POSITIVE_VALUES.items():
        print(f"   - {field}: {len(values)} 个值 {values}")
    
    # 检查检查字段配置
    print(f"📋 检查字段: {len(DUPLICATE_CHECK_FIELDS)} 个")
    print(f"   {DUPLICATE_CHECK_FIELDS}")
    
    print("✅ 配置测试完成\n")

def main():
    """主测试函数"""
    print("🚀 开始测试DBF处理工具增强功能")
    print("=" * 50)
    
    # 测试各个功能
    test_configuration()
    test_false_positive_filtering()
    test_clean_value_comparison()
    test_same_source_analysis()
    
    print("=" * 50)
    print("✅ 所有测试完成！")
    print("\n💡 要测试GUI功能，请运行: python merge_dbf.py")

if __name__ == "__main__":
    main()
