"""
测试DBF智能识别同源数据分析功能的客户身份识别修复
验证同名不同客户的正确识别和文件生成问题修复
"""

import pandas as pd
import os
import tempfile
from merge_dbf import SameSourceDataAnalyzer

def create_customer_identity_test_data():
    """创建包含客户身份识别问题的测试数据"""
    print("📝 创建客户身份识别测试数据...")
    
    test_data = [
        # 场景1：两个不同的"张三"使用相同设备（应识别为同源数据）
        {
            '客户编号': 'CUST001',
            '客户姓名': '张三',
            '资产账户': 'ACC001',
            '站点地址': 'MA;IIP=***********;MAC=AA:BB:CC:DD:EE:01;IMEI=123456789012345',
            'IIP': '***********',
            'MAC': 'AA:BB:CC:DD:EE:01',
            'IMEI': '123456789012345',
            'TEL': '17711102220',
            '发生金额': '100.00',
            '后资金额': '1000.00'
        },
        {
            '客户编号': 'CUST002',  # 不同的客户编号
            '客户姓名': '张三',      # 相同的姓名
            '资产账户': 'ACC002',    # 不同的资产账户
            '站点地址': 'MA;IIP=***********;MAC=AA-BB-CC-DD-EE-01;IMEI=123456789012345',
            'IIP': '***********',   # 相同的IP
            'MAC': 'AA-BB-CC-DD-EE-01',  # 相同的MAC（不同格式）
            'IMEI': '123456789012345',   # 相同的IMEI
            'TEL': '17711102220',
            '发生金额': '200.00',
            '后资金额': '2000.00'
        },
        
        # 场景2：同一个张三的多条记录（不应识别为同源数据）
        {
            '客户编号': 'CUST001',  # 相同的客户编号
            '客户姓名': '张三',
            '资产账户': 'ACC001',
            '站点地址': 'MA;IIP=***********;MAC=AA:BB:CC:DD:EE:01',
            'IIP': '***********',
            'MAC': 'AA:BB:CC:DD:EE:01',
            'IMEI': '123456789012345',
            'TEL': '17711102220',
            '发生金额': '150.00',
            '后资金额': '1150.00'
        },
        
        # 场景3：两个不同的"李四"使用不同设备（独立数据）
        {
            '客户编号': 'CUST003',
            '客户姓名': '李四',
            '资产账户': 'ACC003',
            '站点地址': 'PC;IMEI=987654321098765;MAC=BB:CC:DD:EE:FF:02',
            'IMEI': '987654321098765',
            'MAC': 'BB:CC:DD:EE:FF:02',
            'TEL': '13900139001',
            '发生金额': '300.00',
            '后资金额': '3000.00'
        },
        {
            '客户编号': 'CUST004',
            '客户姓名': '李四',      # 相同姓名
            '资产账户': 'ACC004',    # 不同账户
            '站点地址': 'PC;IMEI=555666777888999;MAC=CC:DD:EE:FF:AA:03',
            'IMEI': '555666777888999',  # 不同IMEI
            'MAC': 'CC:DD:EE:FF:AA:03', # 不同MAC
            'TEL': '13900139002',
            '发生金额': '400.00',
            '后资金额': '4000.00'
        },
        
        # 场景4：没有客户编号的数据（测试基础字段组合策略）
        {
            '客户姓名': '王五',
            '资产账户': 'ACC005',
            '站点地址': 'BK;TEL=13800138001;MAC=DD:EE:FF:AA:BB:04',
            'TEL': '13800138001',
            'MAC': 'DD:EE:FF:AA:BB:04',
            '发生金额': '500.00',
            '后资金额': '5000.00'
        },
        {
            '客户姓名': '王五',      # 相同姓名
            '资产账户': 'ACC006',    # 不同账户
            '站点地址': 'BK;TEL=13800138001;MAC=DD-EE-FF-AA-BB-04',
            'TEL': '13800138001',   # 相同TEL
            'MAC': 'DD-EE-FF-AA-BB-04',  # 相同MAC（不同格式）
            '发生金额': '600.00',
            '后资金额': '6000.00'
        }
    ]
    
    return pd.DataFrame(test_data)

def create_no_customer_id_test_data():
    """创建没有客户编号的测试数据"""
    print("📝 创建无客户编号测试数据...")
    
    test_data = [
        # 只有基础客户字段的数据
        {
            '客户姓名': '张三',
            '资产账户': 'ACC001',
            'MAC': 'AA:BB:CC:DD:EE:01',
            'IMEI': '123456789012345',
            '发生金额': '100.00',
            '后资金额': '1000.00'
        },
        {
            '客户姓名': '张三',      # 相同姓名
            '资产账户': 'ACC002',    # 不同账户（应识别为不同客户）
            'MAC': 'AA-BB-CC-DD-EE-01',  # 相同MAC（应识别为同源数据）
            'IMEI': '123456789012345',   # 相同IMEI
            '发生金额': '200.00',
            '后资金额': '2000.00'
        }
    ]
    
    return pd.DataFrame(test_data)

def test_customer_identity_with_id():
    """测试有客户编号的情况"""
    print("\n🧪 测试客户身份识别（有客户编号）")
    print("-" * 60)
    
    # 创建测试数据
    test_df = create_customer_identity_test_data()
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig') as f:
        test_df.to_csv(f.name, index=False, encoding='utf-8-sig')
        temp_file = f.name
    
    try:
        print(f"📂 测试文件: {os.path.basename(temp_file)}")
        print(f"📊 测试数据: {len(test_df)} 行记录")
        print(f"👥 客户分布:")
        print(f"   - 张三 (CUST001): 2条记录")
        print(f"   - 张三 (CUST002): 1条记录")
        print(f"   - 李四 (CUST003): 1条记录")
        print(f"   - 李四 (CUST004): 1条记录")
        print(f"   - 王五 (无编号): 2条记录")
        
        # 执行分析
        analyzer = SameSourceDataAnalyzer(temp_file)
        analyzer.run_analysis()
        
        # 验证结果
        print(f"\n📋 客户身份识别验证:")
        print(f"   - 识别策略: {analyzer.customer_identity_strategy}")
        print(f"   - 使用字段: {analyzer.available_customer_fields}")
        
        # 验证同源数据识别结果
        expected_same_source = {
            'IIP': 1,    # 两个不同张三共享***********
            'MAC': 2,    # 两个不同张三共享MAC + 两个王五共享MAC
            'IMEI': 1,   # 两个不同张三共享IMEI
            'TEL': 2,    # 两个不同张三共享TEL + 两个王五共享TEL
        }
        
        verification_passed = True
        
        print(f"\n📊 同源数据识别结果:")
        for field, expected_count in expected_same_source.items():
            actual_count = len(analyzer.duplicate_groups.get(field, []))
            status = "✅" if actual_count == expected_count else "❌"
            if actual_count != expected_count:
                verification_passed = False
            
            print(f"   {status} {field}: 期望 {expected_count} 组，实际 {actual_count} 组")
            
            if field in analyzer.duplicate_groups:
                for i, group in enumerate(analyzer.duplicate_groups[field], 1):
                    customer_count = group['customer_count']
                    record_count = group['record_count']
                    clean_value = group['clean_value']
                    print(f"     组{i}: 值'{clean_value}' → {customer_count}个客户，{record_count}条记录")
        
        # 验证文件生成
        file_size = os.path.getsize(analyzer.output_file) if os.path.exists(analyzer.output_file) else 0
        print(f"\n📄 文件生成验证:")
        print(f"   - 文件存在: {os.path.exists(analyzer.output_file)}")
        print(f"   - 文件大小: {file_size:,} 字节 ({file_size / 1024:.2f} KB)")

        # 调整文件大小验证阈值，对于小数据集，5KB以上就是正常的
        if file_size < 5 * 1024:  # 小于5KB
            print(f"   ⚠️ 文件大小异常小，可能存在生成问题")
            verification_passed = False
        else:
            print(f"   ✅ 文件大小正常（对于测试数据集）")
        
        return verification_passed, analyzer.output_file
        
    except Exception as e:
        print(f"❌ 测试过程出错：{str(e)}")
        return False, None
    finally:
        # 清理临时文件
        if os.path.exists(temp_file):
            os.unlink(temp_file)

def test_customer_identity_without_id():
    """测试没有客户编号的情况"""
    print("\n🧪 测试客户身份识别（无客户编号）")
    print("-" * 60)
    
    # 创建测试数据
    test_df = create_no_customer_id_test_data()
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig') as f:
        test_df.to_csv(f.name, index=False, encoding='utf-8-sig')
        temp_file = f.name
    
    try:
        print(f"📂 测试文件: {os.path.basename(temp_file)}")
        print(f"📊 测试数据: {len(test_df)} 行记录")
        
        # 执行分析
        analyzer = SameSourceDataAnalyzer(temp_file)
        analyzer.run_analysis()
        
        # 验证结果
        print(f"\n📋 客户身份识别验证:")
        print(f"   - 识别策略: {analyzer.customer_identity_strategy}")
        print(f"   - 使用字段: {analyzer.available_customer_fields}")
        
        # 在没有客户编号的情况下，相同姓名+不同账户应被识别为不同客户
        expected_same_source = {
            'MAC': 1,    # 两个不同张三（不同账户）共享MAC
            'IMEI': 1,   # 两个不同张三（不同账户）共享IMEI
        }
        
        verification_passed = True
        
        print(f"\n📊 同源数据识别结果:")
        for field, expected_count in expected_same_source.items():
            actual_count = len(analyzer.duplicate_groups.get(field, []))
            status = "✅" if actual_count == expected_count else "❌"
            if actual_count != expected_count:
                verification_passed = False
            
            print(f"   {status} {field}: 期望 {expected_count} 组，实际 {actual_count} 组")
        
        return verification_passed, analyzer.output_file
        
    except Exception as e:
        print(f"❌ 测试过程出错：{str(e)}")
        return False, None
    finally:
        # 清理临时文件
        if os.path.exists(temp_file):
            os.unlink(temp_file)

def main():
    """主测试函数"""
    print("=" * 80)
    print("DBF智能识别同源数据分析功能 - 客户身份识别修复验证")
    print("=" * 80)
    
    # 测试有客户编号的情况
    test1_passed, result_file1 = test_customer_identity_with_id()
    
    # 测试没有客户编号的情况
    test2_passed, result_file2 = test_customer_identity_without_id()
    
    # 总结测试结果
    print(f"\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    if test1_passed and test2_passed:
        print("🎉 所有测试通过！客户身份识别修复成功！")
        print("✅ 有客户编号情况处理正确")
        print("✅ 无客户编号情况处理正确")
        print("✅ 同名不同客户识别正确")
        print("✅ 文件生成大小正常")
        
        print(f"\n💡 修复成果总结:")
        print(f"   1. 智能客户身份识别策略")
        print(f"   2. 优先使用唯一客户标识符")
        print(f"   3. 支持组合字段识别策略")
        print(f"   4. 同名不同客户正确区分")
        print(f"   5. 文件生成验证机制")
        
        if result_file1 and os.path.exists(result_file1):
            print(f"\n📄 测试报告文件: {result_file1}")
        
    else:
        print("❌ 测试失败，需要进一步修复")
        if not test1_passed:
            print("❌ 有客户编号情况存在问题")
        if not test2_passed:
            print("❌ 无客户编号情况存在问题")

if __name__ == "__main__":
    main()
