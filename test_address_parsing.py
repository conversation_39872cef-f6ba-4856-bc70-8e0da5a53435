#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
站点地址解析功能测试脚本
用于验证新的解析逻辑是否符合要求
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from merge_dbf import DBFMergerPro

def test_address_parsing():
    """测试站点地址解析功能"""
    
    # 创建测试实例
    merger = DBFMergerPro()
    
    # 测试用例
    test_cases = [
        # 测试用例1：您提供的示例
        {
            "input": "PC;IIP=************;IPORT=55101;LIP=*************;MAC=C53F15204522;HD=202502405423354354;PCN=HT15FRDHRTE0HTR5;CPU=BFEBFBFF000145Q2;PI=C^NTFS^100G;VOL=51A9-0052@GM=0;OPS=北京云-40;VER=0.0.10;OSV=ICE PACK 1 (BUI",
            "expected_fields": ["设备类型", "IIP", "IPORT", "LIP", "MAC", "HD", "PCN", "CPU", "PI", "VOL", "OPS", "VER", "OSV"],
            "expected_values": {
                "设备类型": "PC",
                "IIP": "************",
                "IPORT": "55101",
                "LIP": "*************",
                "MAC": "C53F15204522",
                "HD": "202502405423354354",
                "PCN": "HT15FRDHRTE0HTR5",
                "CPU": "BFEBFBFF000145Q2",
                "PI": "C^NTFS^100G",
                "VOL": "51A9-0052@GM=0",
                "OPS": "北京云-40",
                "VER": "0.0.10",
                "OSV": "ICE PACK 1 (BUI"
            }
        },
        
        # 测试用例2：包含IDFV的情况
        {
            "input": "MA;IIP=************;IPORT=0011;IDFV=CE9E4EFC-9A3E-49CB-BE6B-0152036521E2;IMEI=NA",
            "expected_fields": ["设备类型", "IIP", "IPORT", "IDFV", "IMEI"],
            "expected_values": {
                "设备类型": "MA",
                "IIP": "************",
                "IPORT": "0011",
                "IDFV": "CE9E4EFC-9A3E-49CB-BE6B-0152036521E2",
                "IMEI": "NA"
            }
        },
        
        # 测试用例3：MobileMA设备类型
        {
            "input": "MobileMA;IIP=***********;IPORT=8080;MAC=ABCDEFGH",
            "expected_fields": ["设备类型", "IIP", "IPORT", "MAC"],
            "expected_values": {
                "设备类型": "MobileMA",
                "IIP": "***********",
                "IPORT": "8080",
                "MAC": "ABCDEFGH"
            }
        },
        
        # 测试用例4：空值测试
        {
            "input": "",
            "expected_fields": [],
            "expected_values": {}
        },
        
        # 测试用例5：没有设备类型，直接是键值对
        {
            "input": "IIP=********;IPORT=9999;MAC=123456",
            "expected_fields": ["IIP", "IPORT", "MAC"],
            "expected_values": {
                "IIP": "********",
                "IPORT": "9999",
                "MAC": "123456"
            }
        },
        
        # 测试用例6：包含其他未处理信息
        {
            "input": "BK;IIP=*******;IPORT=80;未知信息;另一个未知",
            "expected_fields": ["设备类型", "IIP", "IPORT", "其他站点信息"],
            "expected_values": {
                "设备类型": "BK",
                "IIP": "*******",
                "IPORT": "80",
                "其他站点信息": "未知信息;另一个未知"
            }
        }
    ]
    
    print("🧪 开始测试站点地址解析功能...\n")
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试用例 {i}:")
        print(f"输入: {test_case['input']}")
        
        # 执行解析
        result = merger._recursive_split(test_case['input'])
        
        print(f"解析结果: {result}")
        print(f"期望字段: {test_case['expected_fields']}")
        print(f"期望值: {test_case['expected_values']}")
        
        # 验证字段是否正确
        result_fields = set(result.keys())
        expected_fields = set(test_case['expected_fields'])
        
        if result_fields == expected_fields:
            print("✅ 字段匹配正确")
        else:
            print(f"❌ 字段不匹配!")
            print(f"   实际字段: {result_fields}")
            print(f"   期望字段: {expected_fields}")
            print(f"   缺失字段: {expected_fields - result_fields}")
            print(f"   多余字段: {result_fields - expected_fields}")
            all_passed = False
        
        # 验证值是否正确
        values_correct = True
        for field, expected_value in test_case['expected_values'].items():
            if field in result:
                if result[field] != expected_value:
                    print(f"❌ 字段 '{field}' 值不匹配: 实际='{result[field]}', 期望='{expected_value}'")
                    values_correct = False
            else:
                print(f"❌ 缺失字段 '{field}'")
                values_correct = False
        
        if values_correct:
            print("✅ 字段值匹配正确")
        else:
            all_passed = False
        
        print("-" * 60)
    
    if all_passed:
        print("\n🎉 所有测试用例通过！站点地址解析功能正常工作。")
    else:
        print("\n❌ 部分测试用例失败，请检查解析逻辑。")
    
    return all_passed

if __name__ == "__main__":
    test_address_parsing()
