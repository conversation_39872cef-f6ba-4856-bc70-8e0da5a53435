"""
测试智能识别同源数据分析功能对特殊字符的处理能力
验证修复后的功能能正确处理包含控制字符、乱码等问题数据
"""

import pandas as pd
import os
import tempfile
from merge_dbf import SameSourceDataAnalyzer

def create_problematic_test_data():
    """创建包含问题字符的测试数据"""
    print("📝 创建包含特殊字符的测试数据...")
    
    # 模拟原始错误数据
    problematic_data = [
        {
            '客户姓名': '张三',
            '资产账户': 'ACC001',
            '站点地址': 'PC;IIP=***********;IPORT=80;LIP=***********;MAC=015200186B13;HD=鱂?鱂侭 .鱂侐鱂L*;PCN=998502-4103;CPU=POPOPO9989PPPO0;PI=C^NTFS^25G;VOL=A644-D009@GM=1;OPS=东莞-17;VER=6.73.0;OSV=ERVICE PACK 3',
            'IIP': '***********',
            'MAC': '015200186B13',
            'HD': '鱂?鱂侭 .鱂侐鱂L*',  # 包含乱码字符
            'PCN': '998502-4103',
            'CPU': 'POPOPO9989PPPO0',
            '发生金额': '100.50',
            '后资金额': '1000.50'
        },
        {
            '客户姓名': '李四',
            '资产账户': 'ACC002',
            '站点地址': 'PC;IIP=***********;MAC=015200186B13;HD=\x01\x02\x03控制字符\x04\x05;CPU=POPOPO9989PPPO0',  # 包含控制字符
            'IIP': '***********',  # 相同IP，应该被识别为重复
            'MAC': '015200186B13',  # 相同MAC，应该被识别为重复
            'HD': '\x01\x02\x03控制字符\x04\x05',  # 包含控制字符
            'CPU': 'POPOPO9989PPPO0',  # 相同CPU，应该被识别为重复
            '发生金额': '200.75',
            '后资金额': '2000.75'
        },
        {
            '客户姓名': '王五',
            '资产账户': 'ACC003',
            '站点地址': 'MA;IMEI=\x00\x1f超长字符串' + 'A' * 50000 + '\x7f\xff;TEL=13900139001',  # 超长字符串和特殊字符
            'IMEI': '\x00\x1f超长字符串' + 'A' * 50000 + '\x7f\xff',  # 超长字符串
            'TEL': '13900139001',
            '发生金额': '300.25',
            '后资金额': '3000.25'
        },
        {
            '客户姓名': '赵六',
            '资产账户': 'ACC004',
            '站点地址': 'BK;IP=\t\n\r换行制表符\v\f;UDID=\\/:*?"<>|非法字符',  # 包含制表符、换行符和非法字符
            'IP': '\t\n\r换行制表符\v\f',
            'UDID': '\\/:*?"<>|非法字符',  # Excel工作表名称非法字符
            '发生金额': '400.00',
            '后资金额': '4000.00'
        },
        {
            '客户姓名': '孙七',
            '资产账户': 'ACC005',
            '站点地址': 'MI;MAC=正常数据;IP=***********',
            'MAC': '正常数据',
            'IP': '***********',
            '发生金额': '500.99',
            '后资金额': '5000.99'
        }
    ]
    
    return pd.DataFrame(problematic_data)

def test_special_characters_handling():
    """测试特殊字符处理能力"""
    print("🧪 开始测试特殊字符处理能力")
    print("=" * 60)
    
    # 创建问题数据
    test_df = create_problematic_test_data()
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig') as f:
        test_df.to_csv(f.name, index=False, encoding='utf-8-sig')
        temp_file = f.name
    
    try:
        print(f"📂 测试文件创建：{temp_file}")
        print(f"📊 测试数据行数：{len(test_df)}")
        
        # 显示数据中的问题字符
        print("\n🔍 数据中的特殊字符分析：")
        for col in test_df.columns:
            if test_df[col].dtype == 'object':
                for idx, value in test_df[col].items():
                    if pd.notna(value):
                        value_str = str(value)
                        # 检查控制字符
                        control_chars = [char for char in value_str if ord(char) < 32 and char not in '\t\n\r']
                        if control_chars:
                            print(f"   列 '{col}' 行 {idx}: 包含 {len(control_chars)} 个控制字符")
                        
                        # 检查超长字符串
                        if len(value_str) > 1000:
                            print(f"   列 '{col}' 行 {idx}: 超长字符串 ({len(value_str)} 字符)")
        
        # 执行分析
        print(f"\n🚀 开始执行智能分析...")
        analyzer = SameSourceDataAnalyzer(temp_file)
        analyzer.run_analysis()
        
        # 验证结果
        print(f"\n✅ 分析结果验证：")
        print(f"   - 输出文件：{analyzer.output_file}")
        print(f"   - Excel文件存在：{os.path.exists(analyzer.output_file)}")
        
        # 检查是否生成了备用CSV文件
        base_name = os.path.splitext(analyzer.output_file)[0]
        csv_files = [f for f in os.listdir(os.path.dirname(base_name)) if f.startswith(os.path.basename(base_name)) and f.endswith('.csv')]
        if csv_files:
            print(f"   - 生成CSV备用文件：{len(csv_files)} 个")
            for csv_file in csv_files:
                print(f"     * {csv_file}")
        
        # 检查是否生成了诊断报告
        diagnostic_file = analyzer.output_file.replace('.xlsx', '_错误诊断.txt')
        if os.path.exists(diagnostic_file):
            print(f"   - 生成诊断报告：{diagnostic_file}")
        
        if analyzer.duplicate_groups:
            print(f"   - 发现重复字段：{list(analyzer.duplicate_groups.keys())}")
            for field, groups in analyzer.duplicate_groups.items():
                print(f"     * {field}: {len(groups)} 组重复")
        else:
            print("   - 未发现重复数据")
        
        print(f"   - 数据质量评分：{analyzer.validation_results.get('data_quality_score', 'N/A')}")
        print(f"   - 验证问题数量：{analyzer.validation_results.get('total_issues', 'N/A')}")
        
        return analyzer.output_file
        
    except Exception as e:
        print(f"❌ 测试过程出错：{str(e)}")
        return None
    finally:
        # 清理临时文件
        if os.path.exists(temp_file):
            os.unlink(temp_file)

def test_data_cleaning_functions():
    """测试数据清理函数"""
    print("\n🧹 测试数据清理函数")
    print("-" * 40)
    
    analyzer = SameSourceDataAnalyzer("dummy.csv")
    
    # 测试字符串清理
    test_cases = [
        ("正常文本", "正常文本"),
        ("\x01\x02控制字符\x03\x04", "控制字符"),
        ("超长文本" + "A" * 100, "超长文本" + "A" * 100),
        ("\t制表符\n换行符\r回车符", "\t制表符\n换行符\r回车符"),
        ("\\/:*?\"<>|", "\\/:*?\"<>|"),
        ("", ""),
        (None, ""),
    ]
    
    print("字符串清理测试：")
    for original, expected in test_cases:
        try:
            cleaned = analyzer._clean_excel_string(original)
            status = "✅" if cleaned != original or original in [None, ""] else "🔧"
            print(f"   {status} '{repr(original)}' -> '{repr(cleaned)}'")
        except Exception as e:
            print(f"   ❌ '{repr(original)}' -> 错误: {str(e)}")
    
    # 测试工作表名称清理
    worksheet_test_cases = [
        ("正常工作表", "正常工作表"),
        ("包含\\/:*?\"<>|的名称", "包含_______的名称"),
        ("超长工作表名称" + "A" * 50, "超长工作表名称" + "A" * 50),
        ("", "数据表"),
        (None, "数据表"),
    ]
    
    print("\n工作表名称清理测试：")
    for original, _ in worksheet_test_cases:
        try:
            cleaned = analyzer._clean_worksheet_name(original)
            print(f"   ✅ '{original}' -> '{cleaned}' (长度: {len(cleaned)})")
        except Exception as e:
            print(f"   ❌ '{original}' -> 错误: {str(e)}")

def main():
    """主测试函数"""
    print("=" * 80)
    print("智能识别同源数据分析功能 - 特殊字符处理能力测试")
    print("=" * 80)
    
    # 测试数据清理函数
    test_data_cleaning_functions()
    
    # 测试完整流程
    result_file = test_special_characters_handling()
    
    if result_file:
        print(f"\n🎉 测试完成！")
        print(f"📄 结果文件：{result_file}")
        print(f"\n💡 测试总结：")
        print(f"   ✅ 特殊字符处理功能正常")
        print(f"   ✅ 错误处理机制有效")
        print(f"   ✅ 备用报告生成功能正常")
        print(f"   ✅ 诊断报告功能正常")
    else:
        print(f"\n❌ 测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
