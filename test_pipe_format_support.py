#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管道分隔符格式支持测试
验证新增的管道(|)分隔符解析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from merge_dbf import DBFMergerPro
import pandas as pd

def test_pipe_format_detection():
    """测试管道格式检测功能"""
    print("🧪 测试管道格式检测功能...\n")
    
    merger = DBFMergerPro()
    
    test_cases = [
        {
            "name": "标准分号格式",
            "input": "MA;IIP=*******;MAC=ABCDEF;RMPN=13800138000",
            "expected_separator": ";",
            "description": "应该检测为分号格式"
        },
        {
            "name": "标准管道格式",
            "input": "MA|IIP=*************|IPORT=443|LIP=NA|MAC=NA",
            "expected_separator": "|",
            "description": "应该检测为管道格式"
        },
        {
            "name": "复杂管道格式",
            "input": "MI|IIP=*************|IPORT=443|LIP=NA|MAC=NA|IDFV=NA|RMPN=NA|UMPN=NA|ICCID=NA|OSV=NA|IMSI=NA|NA|mobile",
            "expected_separator": "|",
            "description": "应该检测为管道格式"
        },
        {
            "name": "仅设备类型",
            "input": "PC",
            "expected_separator": ";",
            "description": "默认为分号格式"
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试用例 {i}: {test_case['name']}")
        print(f"输入: {test_case['input']}")
        print(f"描述: {test_case['description']}")
        
        # 测试格式检测
        separator, parts = merger._detect_format_and_split(test_case['input'])
        
        print(f"检测到的分隔符: '{separator}'")
        print(f"分割结果: {parts}")
        
        if separator == test_case['expected_separator']:
            print("✅ 格式检测正确")
        else:
            print(f"❌ 格式检测错误，期望: '{test_case['expected_separator']}', 实际: '{separator}'")
            all_passed = False
        
        print("-" * 60)
    
    return all_passed

def test_pipe_format_parsing():
    """测试管道格式解析功能"""
    print("\n🎯 测试管道格式解析功能...\n")
    
    merger = DBFMergerPro()
    
    test_cases = [
        {
            "name": "标准管道格式解析",
            "input": "MA|IIP=*************|IPORT=443|LIP=NA|MAC=NA|IMEI=NA|RMPN=NA|UMPN=NA|ICCID=NA|OSV=NA|IMSI=NA",
            "expected_fields": {
                "设备类型": "MA",
                "IIP": "*************",
                "IPORT": "443",
                "LIP": "NA",
                "MAC": "NA",
                "IMEI": "NA",
                "RMPN": "NA",
                "UMPN": "NA",
                "ICCID": "NA",
                "OSV": "NA",
                "IMSI": "NA"
            }
        },
        {
            "name": "MI设备管道格式",
            "input": "MI|IIP=*************|IPORT=443|LIP=NA|MAC=NA|IDFV=NA|RMPN=NA|UMPN=NA|ICCID=NA|OSV=NA|IMSI=NA",
            "expected_fields": {
                "设备类型": "MI",
                "IIP": "*************",
                "IPORT": "443",
                "LIP": "NA",
                "MAC": "NA",
                "IDFV": "NA",
                "RMPN": "NA",
                "UMPN": "NA",
                "ICCID": "NA",
                "OSV": "NA",
                "IMSI": "NA"
            }
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试用例 {i}: {test_case['name']}")
        print(f"输入: {test_case['input']}")
        
        result = merger._enhanced_recursive_split(test_case['input'])
        
        print(f"解析结果: {result}")
        print(f"期望结果: {test_case['expected_fields']}")
        
        # 验证每个字段
        parsing_correct = True
        for field, expected_value in test_case['expected_fields'].items():
            if field not in result:
                print(f"❌ 缺失字段: {field}")
                parsing_correct = False
                all_passed = False
            elif result[field] != expected_value:
                print(f"❌ 字段 '{field}' 值不匹配:")
                print(f"   期望: '{expected_value}'")
                print(f"   实际: '{result[field]}'")
                parsing_correct = False
                all_passed = False
        
        if parsing_correct:
            print("✅ 解析结果正确")
        
        print("-" * 60)
    
    return all_passed

def test_special_imsi_pattern():
    """测试特殊IMSI模式处理"""
    print("\n🔍 测试特殊IMSI模式处理...\n")
    
    merger = DBFMergerPro()
    
    test_cases = [
        {
            "name": "标准IMSI=NA|NA|mobile模式",
            "input": "MA|IIP=*************|IPORT=443|LIP=NA|MAC=NA|IMEI=NA|RMPN=NA|UMPN=NA|ICCID=NA|OSV=NA|IMSI=NA|NA|mobile",
            "expected_imsi": "NA|NA|mobile",
            "description": "IMSI字段应该包含完整的'NA|NA|mobile'值"
        },
        {
            "name": "MI设备IMSI=NA|NA|mobile模式",
            "input": "MI|IIP=*************|IPORT=443|LIP=NA|MAC=NA|IDFV=NA|RMPN=NA|UMPN=NA|ICCID=NA|OSV=NA|IMSI=NA|NA|mobile",
            "expected_imsi": "NA|NA|mobile",
            "description": "MI设备的IMSI字段应该包含完整的'NA|NA|mobile'值"
        },
        {
            "name": "普通IMSI值",
            "input": "MA|IIP=*******|IMSI=123456789012345|MAC=ABCDEF",
            "expected_imsi": "123456789012345",
            "description": "普通IMSI值应该正常解析"
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试用例 {i}: {test_case['name']}")
        print(f"输入: {test_case['input']}")
        print(f"描述: {test_case['description']}")
        
        result = merger._enhanced_recursive_split(test_case['input'])
        
        print(f"解析结果中的IMSI: '{result.get('IMSI', 'N/A')}'")
        print(f"期望的IMSI值: '{test_case['expected_imsi']}'")
        
        if result.get('IMSI') == test_case['expected_imsi']:
            print("✅ IMSI模式处理正确")
        else:
            print("❌ IMSI模式处理错误")
            all_passed = False
        
        print("-" * 60)
    
    return all_passed

def test_mixed_format_compatibility():
    """测试混合格式兼容性"""
    print("\n🔄 测试混合格式兼容性...\n")

    merger = DBFMergerPro()

    # 设置active_columns以包含所有可能的字段
    merger.active_columns = {
        "设备类型", "IIP", "IPORT", "LIP", "MAC", "IMEI", "RMPN", "UMPN",
        "ICCID", "OSV", "IMSI", "IDFV", "CPU", "HD"
    }
    
    # 模拟混合格式的数据集
    mixed_data = [
        {
            "ID": 1,
            "站点地址": "MA;IIP=*******;MAC=ABCDEF;RMPN=13800138000",  # 分号格式
            "类型": "分号格式"
        },
        {
            "ID": 2,
            "站点地址": "MA|IIP=*************|IPORT=443|LIP=NA|MAC=NA|IMEI=NA|RMPN=NA|UMPN=NA|ICCID=NA|OSV=NA|IMSI=NA|NA|mobile",  # 管道格式
            "类型": "管道格式"
        },
        {
            "ID": 3,
            "站点地址": "PC;IIP=*******;CPU=123456789;HD=987654321",  # 分号格式
            "类型": "分号格式"
        },
        {
            "ID": 4,
            "站点地址": "MI|IIP=*******|IDFV=ABCD-1234|OSV=iOS",  # 管道格式
            "类型": "管道格式"
        }
    ]
    
    print("处理混合格式数据集:")
    
    processed_data = []
    all_passed = True
    
    for record in mixed_data:
        print(f"\n记录 {record['ID']} ({record['类型']}):")
        print(f"  原始地址: {record['站点地址']}")
        
        processed = merger._process_record(record)
        processed_data.append(processed)
        
        # 验证基本解析
        if "设备类型" in processed:
            print(f"  设备类型: {processed['设备类型']}")
        if "IIP" in processed:
            print(f"  IIP: {processed['IIP']}")
        
        # 验证特殊字段
        if record['类型'] == "管道格式" and record['ID'] == 2:
            # 检查IMSI特殊模式
            if processed.get('IMSI') == 'NA|NA|mobile':
                print("  ✅ IMSI特殊模式处理正确")
            else:
                print(f"  ❌ IMSI特殊模式处理错误: {processed.get('IMSI')}")
                all_passed = False
    
    # 创建DataFrame测试
    df = pd.DataFrame(processed_data)
    print(f"\n📊 处理后的DataFrame:")
    print(f"总列数: {len(df.columns)}")
    print(f"包含的解析字段: {[col for col in df.columns if col not in ['ID', '站点地址', '类型']]}")
    
    return all_passed

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n⏪ 测试向后兼容性...\n")
    
    merger = DBFMergerPro()
    
    # 使用之前的测试用例确保没有回归
    legacy_test_cases = [
        {
            "name": "经典分号格式",
            "input": "PC;IIP=************;IPORT=55101;LIP=*************;MAC=C53F15204522;HD=202502405423354354",
            "expected_device": "PC",
            "expected_fields": ["IIP", "IPORT", "LIP", "MAC", "HD"]
        },
        {
            "name": "11位纯数字",
            "input": "13800138000",
            "expected_other_info": "13800138000"
        },
        {
            "name": "特殊符号处理",
            "input": "MI;IDFV=CE9E4EFC-9A3E-49CB-BE6B-0152036521E2;PI=C^NTFS^100G",
            "expected_device": "MI",
            "expected_fields": ["IDFV", "PI"]
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(legacy_test_cases, 1):
        print(f"兼容性测试 {i}: {test_case['name']}")
        print(f"输入: {test_case['input']}")
        
        result = merger._enhanced_recursive_split(test_case['input'])
        
        if "expected_device" in test_case:
            if result.get("设备类型") == test_case["expected_device"]:
                print(f"✅ 设备类型正确: {result.get('设备类型')}")
            else:
                print(f"❌ 设备类型错误: 期望 {test_case['expected_device']}, 实际 {result.get('设备类型')}")
                all_passed = False
        
        if "expected_fields" in test_case:
            missing_fields = [f for f in test_case["expected_fields"] if f not in result]
            if not missing_fields:
                print(f"✅ 所有期望字段存在: {test_case['expected_fields']}")
            else:
                print(f"❌ 缺失字段: {missing_fields}")
                all_passed = False
        
        if "expected_other_info" in test_case:
            if result.get("其他站点信息") == test_case["expected_other_info"]:
                print(f"✅ 其他站点信息正确: {result.get('其他站点信息')}")
            else:
                print(f"❌ 其他站点信息错误")
                all_passed = False
        
        print("-" * 60)
    
    return all_passed

def main():
    """主测试函数"""
    print("🚀 管道分隔符格式支持测试开始\n")
    print("="*80)
    
    # 执行所有测试
    test_results = {
        "格式检测": test_pipe_format_detection(),
        "管道格式解析": test_pipe_format_parsing(),
        "特殊IMSI模式": test_special_imsi_pattern(),
        "混合格式兼容性": test_mixed_format_compatibility(),
        "向后兼容性": test_backward_compatibility()
    }
    
    print("\n" + "="*80)
    print("📊 测试结果总结:")
    
    all_passed = True
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "="*80)
    if all_passed:
        print("🎉 所有管道格式支持测试通过！")
        print("✅ 新功能已成功集成:")
        print("   • 管道(|)分隔符格式自动检测")
        print("   • 管道格式完整解析支持")
        print("   • 特殊IMSI=NA|NA|mobile模式处理")
        print("   • 混合格式数据集兼容")
        print("   • 完全向后兼容分号格式")
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
    
    return all_passed

if __name__ == "__main__":
    main()
