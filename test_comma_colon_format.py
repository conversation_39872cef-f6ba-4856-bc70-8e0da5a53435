#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
逗号冒号格式支持测试
验证新增的逗号冒号(,)分隔符解析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from merge_dbf import DBFMergerPro
import pandas as pd

def test_comma_colon_format_detection():
    """测试逗号冒号格式检测功能"""
    print("🧪 测试逗号冒号格式检测功能...\n")
    
    merger = DBFMergerPro()
    
    test_cases = [
        {
            "name": "标准逗号冒号格式",
            "input": "MAC:02:00:00:00:00:00,UDID:BAAFE101-2JM5-0B5T-04CV-VFDGVFD1S,LIP:************",
            "expected_format": "comma_colon",
            "description": "应该检测为逗号冒号格式"
        },
        {
            "name": "复杂逗号冒号格式",
            "input": "HDInfo=MAC:02:00:00:00:00:00,UDID:BAAFE101-2JM5-0B5T-04CV-VFDGVFD1S,LIP:************,OSV:16.4.2,LIP6:fe80::a11:c3y7:6ef7:01vc,IDFV:5VDS9283P-9C14-75F9-0000-VRDESG67R",
            "expected_format": "comma_colon",
            "description": "包含HDInfo前缀的逗号冒号格式"
        },
        {
            "name": "分号格式",
            "input": "MA;IIP=*******;MAC=ABCDEF",
            "expected_format": "semicolon",
            "description": "应该检测为分号格式"
        },
        {
            "name": "管道格式",
            "input": "MA|IIP=*************|IPORT=443",
            "expected_format": "pipe",
            "description": "应该检测为管道格式"
        },
        {
            "name": "混合符号但非逗号冒号格式",
            "input": "MA;IIP=*******:8080;MAC=ABCDEF",
            "expected_format": "semicolon",
            "description": "包含冒号但主要是分号格式"
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试用例 {i}: {test_case['name']}")
        print(f"输入: {test_case['input']}")
        print(f"描述: {test_case['description']}")
        
        # 测试格式检测
        format_type, parts = merger._detect_format_and_split(test_case['input'])
        
        print(f"检测到的格式: '{format_type}'")
        print(f"分割结果数量: {len(parts)}")
        print(f"前3个部分: {parts[:3]}")
        
        if format_type == test_case['expected_format']:
            print("✅ 格式检测正确")
        else:
            print(f"❌ 格式检测错误，期望: '{test_case['expected_format']}', 实际: '{format_type}'")
            all_passed = False
        
        print("-" * 60)
    
    return all_passed

def test_comma_colon_parsing():
    """测试逗号冒号格式解析功能"""
    print("\n🎯 测试逗号冒号格式解析功能...\n")
    
    merger = DBFMergerPro()
    
    test_cases = [
        {
            "name": "基本逗号冒号格式",
            "input": "MAC:02:00:00:00:00:00,UDID:BAAFE101-2JM5-0B5T-04CV-VFDGVFD1S,LIP:************,OSV:16.4.2",
            "expected_fields": {
                "MAC": "02:00:00:00:00:00",
                "UDID": "BAAFE101-2JM5-0B5T-04CV-VFDGVFD1S",
                "LIP": "************",
                "OSV": "16.4.2"
            }
        },
        {
            "name": "包含IPv6地址的格式",
            "input": "LIP6:fe80::a11:c3y7:6ef7:01vc,IDFV:5VDS9283P-9C14-75F9-0000-VRDESG67R,MAC:AA:BB:CC:DD:EE:FF",
            "expected_fields": {
                "LIP6": "fe80::a11:c3y7:6ef7:01vc",
                "IDFV": "5VDS9283P-9C14-75F9-0000-VRDESG67R",
                "MAC": "AA:BB:CC:DD:EE:FF"
            }
        },
        {
            "name": "用户提供的完整示例",
            "input": "HDInfo=MAC:02:00:00:00:00:00,UDID:BAAFE101-2JM5-0B5T-04CV-VFDGVFD1S,LIP:************,OSV:16.4.2,LIP6:fe80::a11:c3y7:6ef7:01vc,IDFV:5VDS9283P-9C14-75F9-0000-VRDESG67R",
            "expected_fields": {
                "MAC": "02:00:00:00:00:00",
                "UDID": "BAAFE101-2JM5-0B5T-04CV-VFDGVFD1S",
                "LIP": "************",
                "OSV": "16.4.2",
                "LIP6": "fe80::a11:c3y7:6ef7:01vc",
                "IDFV": "5VDS9283P-9C14-75F9-0000-VRDESG67R"
            },
            "note": "HDInfo= 前缀应该被放入其他站点信息"
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试用例 {i}: {test_case['name']}")
        print(f"输入: {test_case['input']}")
        
        result = merger._enhanced_recursive_split(test_case['input'])
        
        print(f"解析结果: {result}")
        print(f"期望字段: {test_case['expected_fields']}")
        
        # 验证每个字段
        parsing_correct = True
        for field, expected_value in test_case['expected_fields'].items():
            if field not in result:
                print(f"❌ 缺失字段: {field}")
                parsing_correct = False
                all_passed = False
            elif result[field] != expected_value:
                print(f"❌ 字段 '{field}' 值不匹配:")
                print(f"   期望: '{expected_value}'")
                print(f"   实际: '{result[field]}'")
                parsing_correct = False
                all_passed = False
        
        # 检查是否有设备类型字段（逗号冒号格式不应该有）
        if "设备类型" in result:
            print(f"⚠️ 逗号冒号格式不应该有设备类型字段: {result['设备类型']}")
        
        if parsing_correct:
            print("✅ 解析结果正确")
        
        if "note" in test_case:
            print(f"📝 注意: {test_case['note']}")
        
        print("-" * 60)
    
    return all_passed

def test_complex_value_preservation():
    """测试复杂值保护功能"""
    print("\n🔍 测试复杂值保护功能...\n")
    
    merger = DBFMergerPro()
    
    test_cases = [
        {
            "name": "MAC地址保护",
            "input": "MAC:02:00:00:00:00:00,TYPE:ethernet",
            "field": "MAC",
            "expected_value": "02:00:00:00:00:00",
            "description": "MAC地址中的冒号应该被保护"
        },
        {
            "name": "IPv6地址保护",
            "input": "LIP6:fe80::a11:c3y7:6ef7:01vc,TYPE:ipv6",
            "field": "LIP6",
            "expected_value": "fe80::a11:c3y7:6ef7:01vc",
            "description": "IPv6地址中的双冒号应该被保护"
        },
        {
            "name": "复杂UDID保护",
            "input": "UDID:BAAFE101-2JM5-0B5T-04CV-VFDGVFD1S,MAC:AA:BB:CC:DD:EE:FF",
            "field": "UDID",
            "expected_value": "BAAFE101-2JM5-0B5T-04CV-VFDGVFD1S",
            "description": "复杂UDID值应该完整保护"
        },
        {
            "name": "时间戳格式保护",
            "input": "TIMESTAMP:2024-01-15T10:30:45.123Z,EVENT:login",
            "field": "TIMESTAMP",
            "expected_value": "2024-01-15T10:30:45.123Z",
            "description": "时间戳中的冒号应该被保护"
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试用例 {i}: {test_case['name']}")
        print(f"输入: {test_case['input']}")
        print(f"描述: {test_case['description']}")
        
        result = merger._enhanced_recursive_split(test_case['input'])
        
        field = test_case['field']
        expected_value = test_case['expected_value']
        actual_value = result.get(field, 'N/A')
        
        print(f"字段 '{field}': '{actual_value}'")
        print(f"期望值: '{expected_value}'")
        
        if actual_value == expected_value:
            print("✅ 复杂值保护正确")
        else:
            print("❌ 复杂值保护失败")
            all_passed = False
        
        print("-" * 60)
    
    return all_passed

def test_three_format_compatibility():
    """测试三种格式兼容性"""
    print("\n🔄 测试三种格式兼容性...\n")
    
    merger = DBFMergerPro()
    
    # 设置active_columns以包含所有可能的字段
    merger.active_columns = {
        "设备类型", "IIP", "IPORT", "LIP", "MAC", "IMEI", "RMPN", "UMPN", 
        "ICCID", "OSV", "IMSI", "IDFV", "CPU", "HD", "VER", "UDID", "LIP6"
    }
    
    # 模拟包含三种格式的数据集
    mixed_data = [
        {
            "ID": 1,
            "站点地址": "MA;IIP=*******;MAC=ABCDEF;RMPN=13800138000",  # 分号格式
            "格式类型": "分号格式"
        },
        {
            "ID": 2,
            "站点地址": "MA|IIP=*************|IPORT=443|LIP=NA|MAC=NA|IMEI=NA|RMPN=NA|UMPN=NA|ICCID=NA|OSV=NA|IMSI=NA|NA|mobile",  # 管道格式
            "格式类型": "管道格式"
        },
        {
            "ID": 3,
            "站点地址": "MAC:02:00:00:00:00:00,UDID:BAAFE101-2JM5-0B5T-04CV-VFDGVFD1S,LIP:************,OSV:16.4.2,LIP6:fe80::a11:c3y7:6ef7:01vc,IDFV:5VDS9283P-9C14-75F9-0000-VRDESG67R",  # 逗号冒号格式
            "格式类型": "逗号冒号格式"
        },
        {
            "ID": 4,
            "站点地址": "PC;IIP=*******;CPU=123456789;HD=987654321",  # 分号格式
            "格式类型": "分号格式"
        }
    ]
    
    print("处理三种格式混合数据集:")
    
    processed_data = []
    all_passed = True
    
    for record in mixed_data:
        print(f"\n记录 {record['ID']} ({record['格式类型']}):")
        print(f"  原始地址: {record['站点地址']}")
        
        processed = merger._process_record(record)
        processed_data.append(processed)
        
        # 验证基本解析
        format_type = record['格式类型']
        
        if format_type in ["分号格式", "管道格式"]:
            if "设备类型" in processed:
                print(f"  设备类型: {processed['设备类型']}")
            else:
                print("  ❌ 缺失设备类型字段")
                all_passed = False
        else:  # 逗号冒号格式
            if "设备类型" not in processed or processed.get("设备类型") == "":
                print("  ✅ 逗号冒号格式正确无设备类型")
            else:
                print(f"  ⚠️ 逗号冒号格式不应该有设备类型: {processed.get('设备类型')}")
        
        # 显示关键字段
        key_fields = ["IIP", "MAC", "UDID", "LIP6"]
        for field in key_fields:
            if field in processed and processed[field]:
                value = processed[field]
                if ":" in value and field in ["MAC", "LIP6"]:
                    print(f"  {field}: {value} ⭐ (复杂值)")
                else:
                    print(f"  {field}: {value}")
    
    # 创建DataFrame测试
    df = pd.DataFrame(processed_data)
    print(f"\n📊 处理后的DataFrame:")
    print(f"总列数: {len(df.columns)}")
    print(f"包含的解析字段: {[col for col in df.columns if col not in ['ID', '站点地址', '格式类型']]}")
    
    return all_passed

def main():
    """主测试函数"""
    print("🚀 逗号冒号分隔符格式支持测试开始\n")
    print("="*80)
    
    # 执行所有测试
    test_results = {
        "格式检测": test_comma_colon_format_detection(),
        "逗号冒号格式解析": test_comma_colon_parsing(),
        "复杂值保护": test_complex_value_preservation(),
        "三种格式兼容性": test_three_format_compatibility()
    }
    
    print("\n" + "="*80)
    print("📊 测试结果总结:")
    
    all_passed = True
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "="*80)
    if all_passed:
        print("🎉 所有逗号冒号格式支持测试通过！")
        print("✅ 新功能已成功集成:")
        print("   • 逗号冒号(,)分隔符格式自动检测")
        print("   • 智能冒号分割（仅第一个冒号）")
        print("   • MAC地址和IPv6地址完整保护")
        print("   • 三种格式混合数据集兼容")
        print("   • 完全向后兼容分号和管道格式")
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
    
    return all_passed

if __name__ == "__main__":
    main()
